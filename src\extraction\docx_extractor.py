#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Word文档内容提取器
使用python-docx提取Word文档的文本、表格等内容
"""

import os
from typing import List, Optional
from docx import Document
from docx.table import Table
from docx.text.paragraph import Paragraph
from .base_extractor import BaseExtractor, ExtractedContent, ExtractedTable, ExtractedParagraph
from ..utils import get_logger

logger = get_logger(__name__)


class DocxExtractor(BaseExtractor):
    """Word文档内容提取器"""
    
    def __init__(self):
        super().__init__()
        self.logger.info("Word提取器初始化完成")
    
    def get_supported_extensions(self) -> List[str]:
        """获取支持的文件扩展名"""
        return ['.docx', '.doc']
    
    def extract_content(self, file_path: str) -> Optional[ExtractedContent]:
        """
        提取Word文档内容
        
        Args:
            file_path: Word文件路径
            
        Returns:
            Optional[ExtractedContent]: 提取的内容
        """
        try:
            if not os.path.exists(file_path):
                self.logger.error(f"Word文件不存在: {file_path}")
                return None
            
            self.logger.info(f"开始提取Word内容: {os.path.basename(file_path)}")
            
            content = ExtractedContent()
            
            # 打开Word文档
            doc = Document(file_path)
            
            # 提取核心属性
            if doc.core_properties:
                content.metadata = self._extract_core_properties(doc.core_properties)
                if doc.core_properties.title:
                    content.title = doc.core_properties.title
            
            # 提取文档内容
            self._extract_document_content(doc, content)
            
            # 按优先级提取产品名称：页眉 → 文件名 → 产品陈述
            content.product_name = self._extract_product_name_with_priority(doc, file_path, content)
            
            self.logger.info(f"Word内容提取完成: {len(content.tables)} 个表格, {len(content.paragraphs)} 个段落")
            return content
            
        except Exception as e:
            self.logger.error(f"Word内容提取失败 {file_path}: {e}")
            return None
    
    def _extract_core_properties(self, core_props) -> dict:
        """提取文档核心属性"""
        metadata = {}
        
        try:
            if core_props.title:
                metadata['title'] = core_props.title
            if core_props.author:
                metadata['author'] = core_props.author
            if core_props.subject:
                metadata['subject'] = core_props.subject
            if core_props.created:
                metadata['created'] = str(core_props.created)
            if core_props.modified:
                metadata['modified'] = str(core_props.modified)
        except Exception as e:
            self.logger.debug(f"核心属性提取失败: {e}")
        
        return metadata
    
    def _extract_document_content(self, doc: Document, content: ExtractedContent):
        """提取文档内容"""
        try:
            # 遍历文档元素
            for element in doc.element.body:
                if element.tag.endswith('tbl'):
                    # 处理表格
                    table = self._find_table_by_element(doc, element)
                    if table:
                        extracted_table = self._extract_table(table)
                        if extracted_table:
                            # 标准化表格结构
                            standardized_table = self.standardize_table_structure(extracted_table)
                            content.tables.append(standardized_table)
                
                elif element.tag.endswith('p'):
                    # 处理段落
                    paragraph = self._find_paragraph_by_element(doc, element)
                    if paragraph:
                        extracted_paragraph = self._extract_paragraph(paragraph)
                        if extracted_paragraph:
                            content.paragraphs.append(extracted_paragraph)
        
        except Exception as e:
            self.logger.debug(f"文档内容提取失败: {e}")
            # 备用方法：直接遍历段落和表格
            self._extract_content_fallback(doc, content)
    
    def _extract_content_fallback(self, doc: Document, content: ExtractedContent):
        """备用内容提取方法"""
        try:
            # 提取所有段落
            for para in doc.paragraphs:
                extracted_paragraph = self._extract_paragraph(para)
                if extracted_paragraph:
                    content.paragraphs.append(extracted_paragraph)

            # 提取所有表格（包括嵌套表格）
            self._extract_all_tables_recursive(doc, content)

        except Exception as e:
            self.logger.debug(f"备用内容提取失败: {e}")

    def _extract_all_tables_recursive(self, doc: Document, content: ExtractedContent):
        """递归提取所有表格，包括嵌套表格"""
        try:
            # 提取顶层表格
            for table in doc.tables:
                extracted_table = self._extract_table(table)
                if extracted_table:
                    # 标准化表格结构
                    standardized_table = self.standardize_table_structure(extracted_table)
                    content.tables.append(standardized_table)

                # 检查表格中的嵌套表格
                self._extract_nested_tables_from_table(table, content)

        except Exception as e:
            self.logger.debug(f"递归表格提取失败: {e}")

    def _extract_nested_tables_from_table(self, table: Table, content: ExtractedContent):
        """从表格中提取嵌套表格"""
        try:
            for row in table.rows:
                for cell in row.cells:
                    # 检查单元格中的嵌套表格
                    for nested_table in cell.tables:
                        nested_extracted = self._extract_table(nested_table)
                        if nested_extracted:
                            # 标记为嵌套表格
                            nested_extracted.position = {'nested': True, 'parent_cell': cell.text[:50]}
                            # 标准化嵌套表格结构
                            standardized_nested = self.standardize_table_structure(nested_extracted)
                            content.tables.append(standardized_nested)
                            self.logger.debug(f"发现嵌套表格: {len(nested_extracted.data)} 行")

        except Exception as e:
            self.logger.debug(f"嵌套表格提取失败: {e}")

    def _find_table_by_element(self, doc: Document, element) -> Optional[Table]:
        """根据元素查找对应的表格对象"""
        try:
            for table in doc.tables:
                if table._element == element:
                    return table
        except Exception:
            pass
        return None
    
    def _find_paragraph_by_element(self, doc: Document, element) -> Optional[Paragraph]:
        """根据元素查找对应的段落对象"""
        try:
            for para in doc.paragraphs:
                if para._element == element:
                    return para
        except Exception:
            pass
        return None
    
    def _extract_table(self, table: Table) -> Optional[ExtractedTable]:
        """提取表格数据（支持嵌套表格）"""
        try:
            table_data = []
            headers = None

            for row_idx, row in enumerate(table.rows):
                row_data = []

                for cell in row.cells:
                    # 检查单元格是否包含嵌套表格
                    nested_tables = cell.tables
                    if nested_tables:
                        # 处理嵌套表格
                        nested_content = self._extract_nested_tables(nested_tables)
                        if nested_content:
                            row_data.append(f"[嵌套表格: {len(nested_tables)}个]")
                            # 将嵌套表格数据作为子表格添加到结果中
                            for nested_table_data in nested_content:
                                table_data.extend(nested_table_data)
                        else:
                            # 如果嵌套表格为空，使用单元格文本（包含上下标处理）
                            cell_text = self._extract_cell_text_with_formatting(cell)
                            row_data.append(cell_text)
                    else:
                        # 提取单元格文本（包含上下标处理）
                        cell_text = self._extract_cell_text_with_formatting(cell)
                        row_data.append(cell_text)

                # 检查是否为有效行
                if self._is_valid_table_row(row_data):
                    # 第一行可能是表头
                    if row_idx == 0 and self._looks_like_header(row_data):
                        headers = row_data
                    else:
                        table_data.append(row_data)

            if table_data:
                return ExtractedTable(
                    page=1,  # Word文档没有页面概念，统一设为1
                    data=table_data,
                    headers=headers
                )

        except Exception as e:
            self.logger.debug(f"表格提取失败: {e}")

        return None

    def _extract_nested_tables(self, nested_tables: list) -> list:
        """提取嵌套表格数据"""
        nested_content = []

        try:
            for nested_table in nested_tables:
                nested_data = []

                for row in nested_table.rows:
                    row_data = []
                    for cell in row.cells:
                        cell_text = self._clean_text(cell.text)
                        row_data.append(cell_text)

                    if self._is_valid_table_row(row_data):
                        nested_data.append(row_data)

                if nested_data:
                    nested_content.append(nested_data)
                    self.logger.debug(f"提取嵌套表格: {len(nested_data)} 行数据")

        except Exception as e:
            self.logger.debug(f"嵌套表格提取失败: {e}")

        return nested_content

    def _extract_cell_text_with_formatting(self, cell) -> str:
        """提取单元格文本，保留上下标格式"""
        try:
            text_parts = []

            for paragraph in cell.paragraphs:
                para_text = self._extract_paragraph_text_with_formatting(paragraph)
                if para_text.strip():
                    text_parts.append(para_text)

            result = ' '.join(text_parts)
            return self._clean_text(result)

        except Exception as e:
            self.logger.debug(f"单元格格式化文本提取失败: {e}")
            return self._clean_text(cell.text)

    def _extract_paragraph_text_with_formatting(self, paragraph) -> str:
        """提取段落文本，保留上下标格式"""
        try:
            text_parts = []

            for run in paragraph.runs:
                text = run.text
                if not text:
                    continue

                # 检查上标
                if run.font.superscript:
                    # 将上标转换为^符号表示
                    text = f"^{text}"
                # 检查下标
                elif run.font.subscript:
                    # 将下标转换为_符号表示
                    text = f"_{text}"

                text_parts.append(text)

            return ''.join(text_parts)

        except Exception as e:
            self.logger.debug(f"段落格式化文本提取失败: {e}")
            return paragraph.text

    def _extract_paragraph(self, paragraph: Paragraph) -> Optional[ExtractedParagraph]:
        """提取段落数据"""
        try:
            text = self._clean_text(paragraph.text)
            
            if self._is_valid_paragraph(text):
                # 尝试获取段落样式
                style = None
                try:
                    if paragraph.style:
                        style = paragraph.style.name
                except Exception:
                    pass
                
                return ExtractedParagraph(
                    page=1,  # Word文档没有页面概念，统一设为1
                    text=text,
                    style=style
                )
        
        except Exception as e:
            self.logger.debug(f"段落提取失败: {e}")
        
        return None

    def _extract_product_name_with_priority(self, doc: Document, file_path: str, content: ExtractedContent) -> str:
        """按优先级提取产品名称：页眉 → 文件名 → 产品陈述"""

        # 优先级1：从页眉提取
        product_name = self._extract_product_name_from_header(doc)
        if product_name:
            self.logger.debug(f"从页眉提取产品名称: {product_name}")
            return product_name

        # 优先级2：从文件名提取
        product_name = self._extract_product_name_from_filename(os.path.basename(file_path))
        if product_name and product_name != "未知产品":
            self.logger.debug(f"从文件名提取产品名称: {product_name}")
            return product_name

        # 优先级3：从产品陈述提取
        product_name = self._extract_product_name_from_statement(content)
        if product_name:
            self.logger.debug(f"从产品陈述提取产品名称: {product_name}")
            return product_name

        # 如果都没有，返回文件名（去掉扩展名）
        return os.path.splitext(os.path.basename(file_path))[0]

    def _extract_product_name_from_header(self, doc: Document) -> str:
        """从页眉提取产品名称"""
        try:
            for section in doc.sections:
                if section.header:
                    for para in section.header.paragraphs:
                        if para.text.strip():
                            header_text = para.text.strip()

                            # 方法1：尝试从标签格式提取（产品名称：XXX）
                            product_name = self._extract_from_label_format(header_text)
                            if product_name:
                                return product_name

                            # 方法2：直接从页眉文本提取产品型号
                            product_name = self._parse_product_name_from_text(header_text)
                            if product_name:
                                return product_name
        except Exception as e:
            self.logger.debug(f"页眉产品名称提取失败: {e}")

        return ""

    def _extract_from_label_format(self, text: str) -> str:
        """从标签格式提取产品名称（如：产品名称：FM-102N-2）"""
        import re

        # 定义各种可能的标签词
        label_variants = [
            '产品名称', '产品名字', '产品名',
            '型号', '产品型号', '产品编号',
            'Product Name', 'Product', 'Model'
        ]

        # 定义各种可能的分隔符
        separators = ['：', ':', '=', '－', '-']

        for label in label_variants:
            for sep in separators:
                # 构建匹配模式
                pattern = rf'{re.escape(label)}\s*{re.escape(sep)}\s*([^\s,，。；\n]+)'
                match = re.search(pattern, text, re.IGNORECASE)
                if match:
                    product_name = match.group(1).strip()
                    if self._is_valid_product_name(product_name):
                        self.logger.debug(f"从标签格式提取: {label}{sep}{product_name}")
                        return product_name

        return ""

    def _extract_product_name_from_statement(self, content: ExtractedContent) -> str:
        """从产品陈述提取产品名称"""
        try:
            # 查找产品陈述相关的表格
            for table in content.tables:
                if table.headers:
                    # 检查表头是否包含产品陈述
                    header_text = ' '.join(table.headers)
                    if any(keyword in header_text for keyword in ['产品陈述', '产品描述', '产品介绍']):
                        # 从表头的第二列（产品陈述内容）提取
                        if len(table.headers) > 1:
                            statement_text = table.headers[1]
                            product_name = self._parse_product_name_from_text(statement_text)
                            if product_name:
                                return product_name

                # 检查表格数据中的产品陈述
                for row in table.data:
                    if len(row) >= 2:
                        first_cell = row[0].strip()
                        if any(keyword in first_cell for keyword in ['产品陈述', '产品描述', '产品介绍']):
                            statement_text = row[1]
                            product_name = self._parse_product_name_from_text(statement_text)
                            if product_name:
                                return product_name

            # 从段落中查找产品陈述
            for para in content.paragraphs:
                if any(keyword in para.text for keyword in ['产品陈述', '产品描述', '产品介绍']):
                    product_name = self._parse_product_name_from_text(para.text)
                    if product_name:
                        return product_name

        except Exception as e:
            self.logger.debug(f"产品陈述产品名称提取失败: {e}")

        return ""

    def _parse_product_name_from_text(self, text: str) -> str:
        """从文本中解析产品名称"""
        import re

        # 产品名称模式（优先匹配更具体的模式）
        patterns = [
            r'(FM-[A-Z0-9]+(?:-[A-Z0-9]+)*)',  # FM-109-17, FM-3602-12 等
            r'([A-Z]{2,}-[0-9]+(?:-[A-Z0-9]+)*)',  # 通用格式：字母-数字-可选后缀
            r'([A-Z]+[0-9]+[A-Z]*)',  # ABC123, FM3602 等
            r'(产品型号[：:]\s*([^\s,，。；]+))',  # 产品型号：XXX
            r'(型号[：:]\s*([^\s,，。；]+))',  # 型号：XXX
        ]

        for pattern in patterns:
            matches = re.findall(pattern, text)
            if matches:
                # 取第一个匹配结果
                match = matches[0]
                if isinstance(match, tuple):
                    # 如果是元组，取最后一个非空元素
                    for item in reversed(match):
                        if item and item.strip():
                            product_name = item.strip()
                            # 验证产品名称的合理性
                            if self._is_valid_product_name(product_name):
                                return product_name
                else:
                    product_name = match.strip()
                    if self._is_valid_product_name(product_name):
                        return product_name

        return ""

    def _is_valid_product_name(self, name: str) -> bool:
        """验证产品名称的合理性"""
        if not name or len(name) < 2:
            return False

        # 排除明显不是产品名称的文本
        invalid_keywords = [
            '技术参数', '参数表', '数据表', '说明书', '手册',
            '工艺', '性能', '测试', '检验', '标准',
            '公司', '有限', '股份', '集团'
        ]

        for keyword in invalid_keywords:
            if keyword in name:
                return False

        # 产品名称通常包含字母和数字
        import re
        if re.search(r'[A-Za-z]', name) and re.search(r'[0-9]', name):
            return True

        # 或者是纯字母但长度合适
        if re.match(r'^[A-Za-z]{2,10}$', name):
            return True

        return False

    def _extract_product_name_from_content(self, content: ExtractedContent) -> str:
        """从内容中提取产品名称"""
        # 首先从段落中查找
        for paragraph in content.paragraphs:
            product_name = self._extract_product_name_from_text(paragraph.text)
            if product_name:
                return product_name
        
        # 然后从表格中查找
        for table in content.tables:
            for row in table.data:
                row_text = ' '.join(row)
                product_name = self._extract_product_name_from_text(row_text)
                if product_name:
                    return product_name
        
        return ""
    
    def _extract_product_name_from_filename(self, filename: str) -> str:
        """从文件名提取产品名称"""
        import re

        # 移除扩展名
        name = os.path.splitext(filename)[0]

        # 先尝试从文件名中提取产品型号
        product_name = self._parse_product_name_from_text(name)
        if product_name:
            return product_name

        # 如果没有找到，移除常见后缀后再试
        suffixes = ['技术参数表', '参数表', '技术参数', 'TDS', 'datasheet', '数据表', '说明书']
        cleaned_name = name
        for suffix in suffixes:
            cleaned_name = re.sub(rf'{suffix}.*$', '', cleaned_name, flags=re.IGNORECASE)

        # 移除日期后缀（如20250101）
        cleaned_name = re.sub(r'\d{8}$', '', cleaned_name)
        cleaned_name = re.sub(r'\d{6}$', '', cleaned_name)

        # 移除括号内容
        cleaned_name = re.sub(r'\([^)]*\)$', '', cleaned_name)
        cleaned_name = re.sub(r'（[^）]*）$', '', cleaned_name)

        cleaned_name = cleaned_name.strip()

        # 再次尝试提取产品名称
        product_name = self._parse_product_name_from_text(cleaned_name)
        if product_name:
            return product_name

        # 如果还是没有，返回清理后的名称
        return cleaned_name if cleaned_name else "未知产品"
    
    def _looks_like_header(self, row: List[str]) -> bool:
        """判断行是否看起来像表头"""
        if not row:
            return False
        
        # 表头通常包含这些关键词
        header_keywords = [
            '项目', '参数', '指标', '性能', '数值', '单位', '值',
            'item', 'parameter', 'property', 'value', 'unit'
        ]
        
        row_text = ' '.join(row).lower()
        return any(keyword in row_text for keyword in header_keywords)
    
    def extract_text_only(self, file_path: str) -> str:
        """
        仅提取Word文档的纯文本内容
        
        Args:
            file_path: Word文件路径
            
        Returns:
            str: 提取的文本内容
        """
        try:
            doc = Document(file_path)
            text_parts = []
            
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    text_parts.append(paragraph.text.strip())
            
            return '\n'.join(text_parts)
            
        except Exception as e:
            self.logger.error(f"Word文本提取失败 {file_path}: {e}")
            return ""
    
    def extract_tables_only(self, file_path: str) -> List[List[List[str]]]:
        """
        仅提取Word文档的表格内容
        
        Args:
            file_path: Word文件路径
            
        Returns:
            List[List[List[str]]]: 提取的表格数据
        """
        try:
            doc = Document(file_path)
            all_tables = []
            
            for table in doc.tables:
                table_data = []
                for row in table.rows:
                    row_data = [cell.text.strip() for cell in row.cells]
                    table_data.append(row_data)
                
                if table_data:
                    all_tables.append(table_data)
            
            return all_tables
            
        except Exception as e:
            self.logger.error(f"Word表格提取失败 {file_path}: {e}")
            return []
