#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基础提取器
定义内容提取的通用接口和数据结构
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
import re
from ..utils import get_logger

logger = get_logger(__name__)


@dataclass
class ExtractedTable:
    """提取的表格数据"""
    page: int
    data: List[List[str]]
    headers: Optional[List[str]] = None
    position: Optional[Dict[str, float]] = None
    table_type: Optional[str] = None  # 表格类型：'ab_component', 'mixed', 'performance', 'packaging'
    confidence: float = 1.0  # 表格识别置信度


@dataclass
class ExtractedParagraph:
    """提取的段落数据"""
    page: int
    text: str
    position: Optional[Dict[str, float]] = None
    style: Optional[str] = None


@dataclass
class ExtractedContent:
    """提取的内容数据结构"""
    title: str = ""
    product_name: str = ""
    tables: List[ExtractedTable] = None
    paragraphs: List[ExtractedParagraph] = None
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.tables is None:
            self.tables = []
        if self.paragraphs is None:
            self.paragraphs = []
        if self.metadata is None:
            self.metadata = {}


class BaseExtractor(ABC):
    """基础提取器抽象类"""

    def __init__(self):
        self.logger = get_logger(__name__)
        # 表格类型识别关键词
        self.table_type_keywords = {
            'ab_component': ['109-17A', '109-17B', 'A组分', 'B组分', 'A剂', 'B剂', '主剂', '固化剂'],
            'performance': ['硬度', '电阻率', '绝缘强度', '拉伸强度', '阻燃', '应用温度'],
            'packaging': ['包装规格', '规格1', '规格2', '规格3', 'A组分包装', 'B组分包装'],
            'process': ['推荐工艺', '储存运输', '重要说明', '使用方法']
        }
    
    @abstractmethod
    def extract_content(self, file_path: str) -> Optional[ExtractedContent]:
        """
        提取文件内容
        
        Args:
            file_path: 文件路径
            
        Returns:
            ExtractedContent: 提取的内容，如果失败返回None
        """
        pass
    
    @abstractmethod
    def get_supported_extensions(self) -> List[str]:
        """
        获取支持的文件扩展名
        
        Returns:
            List[str]: 支持的扩展名列表
        """
        pass
    
    def is_supported_file(self, file_path: str) -> bool:
        """
        检查文件是否支持
        
        Args:
            file_path: 文件路径
            
        Returns:
            bool: 是否支持
        """
        import os
        _, ext = os.path.splitext(file_path.lower())
        return ext in self.get_supported_extensions()
    
    def _clean_text(self, text: str) -> str:
        """
        清理文本内容
        
        Args:
            text: 原始文本
            
        Returns:
            str: 清理后的文本
        """
        if not text:
            return ""
        
        # 移除多余的空白字符
        import re
        cleaned = re.sub(r'\s+', ' ', text.strip())
        
        # 移除控制字符
        cleaned = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f-\x9f]', '', cleaned)
        
        return cleaned
    
    def _extract_product_name_from_text(self, text: str) -> Optional[str]:
        """
        从文本中提取产品名称
        
        Args:
            text: 文本内容
            
        Returns:
            Optional[str]: 产品名称
        """
        import re
        
        # 产品名称模式
        patterns = [
            r'产品名称[：:]\s*([^\n\r]+)',
            r'产品型号[：:]\s*([^\n\r]+)',
            r'型号[：:]\s*([^\n\r]+)',
            r'Product[：:]\s*([^\n\r]+)',
            r'Model[：:]\s*([^\n\r]+)',
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                product_name = match.group(1).strip()
                if product_name:
                    return product_name
        
        return None
    
    def _is_valid_table_row(self, row: List[str]) -> bool:
        """
        检查表格行是否有效
        
        Args:
            row: 表格行数据
            
        Returns:
            bool: 是否有效
        """
        if not row:
            return False
        
        # 检查是否有非空内容
        has_content = any(cell and cell.strip() for cell in row)
        if not has_content:
            return False
        
        # 过滤明显的表头行
        first_cell = str(row[0]).strip().lower()
        header_indicators = ['序号', 'no', 'number', '项目', 'item', '参数', 'parameter']
        if first_cell in header_indicators:
            return False
        
        return True
    
    def _is_valid_paragraph(self, text: str) -> bool:
        """
        检查段落是否有效
        
        Args:
            text: 段落文本
            
        Returns:
            bool: 是否有效
        """
        if not text or len(text.strip()) < 2:
            return False
        
        # 过滤页眉页脚等无用信息
        useless_patterns = [
            r'^\d+$',  # 纯数字（页码）
            r'^第\s*\d+\s*页',  # 页码信息
            r'^\s*[-=_]{3,}\s*$',  # 分隔线
        ]
        
        for pattern in useless_patterns:
            if re.match(pattern, text.strip()):
                return False
        
        return True

    def standardize_table_structure(self, table: ExtractedTable) -> ExtractedTable:
        """
        标准化表格结构

        Args:
            table: 原始表格

        Returns:
            ExtractedTable: 标准化后的表格
        """
        # 识别表格类型
        table.table_type = self._identify_table_type(table)

        # 标准化表头处理
        table = self._standardize_headers(table)

        # 清理和验证数据
        table = self._clean_table_data(table)

        return table

    def _identify_table_type(self, table: ExtractedTable) -> str:
        """识别表格类型"""
        # 检查表头
        if table.headers:
            header_text = ' '.join(table.headers).lower()
            for table_type, keywords in self.table_type_keywords.items():
                if any(keyword.lower() in header_text for keyword in keywords):
                    return table_type

        # 检查数据内容
        all_text = []
        for row in table.data[:5]:  # 只检查前5行
            all_text.extend(row)
        content_text = ' '.join(all_text).lower()

        for table_type, keywords in self.table_type_keywords.items():
            if any(keyword.lower() in content_text for keyword in keywords):
                return table_type

        return 'unknown'

    def _standardize_headers(self, table: ExtractedTable) -> ExtractedTable:
        """标准化表头处理"""
        if not table.data:
            return table

        # 如果没有表头，尝试从第一行提取
        if not table.headers and table.data:
            first_row = table.data[0]
            if self._looks_like_header(first_row):
                table.headers = first_row
                table.data = table.data[1:]  # 移除表头行

        return table

    def _clean_table_data(self, table: ExtractedTable) -> ExtractedTable:
        """清理表格数据"""
        cleaned_data = []

        for row in table.data:
            # 清理每个单元格
            cleaned_row = []
            for cell in row:
                cleaned_cell = self._clean_text(str(cell)) if cell else ""
                cleaned_row.append(cleaned_cell)

            # 只保留有效行
            if self._is_valid_table_row(cleaned_row):
                cleaned_data.append(cleaned_row)

        table.data = cleaned_data
        return table

    def _looks_like_header(self, row: List[str]) -> bool:
        """判断行是否看起来像表头"""
        if not row:
            return False

        # 表头通常包含这些关键词
        header_keywords = [
            '项目', '参数', '指标', '性能', '数值', '单位', '值', '测试项目',
            'item', 'parameter', 'property', 'value', 'unit', 'test'
        ]

        row_text = ' '.join(row).lower()
        return any(keyword in row_text for keyword in header_keywords)

    def get_extraction_summary(self, content: ExtractedContent) -> Dict[str, Any]:
        """
        获取提取摘要信息

        Args:
            content: 提取的内容

        Returns:
            Dict[str, Any]: 摘要信息
        """
        # 统计不同类型的表格
        table_types = {}
        for table in content.tables:
            table_type = table.table_type or 'unknown'
            table_types[table_type] = table_types.get(table_type, 0) + 1

        return {
            'title': content.title,
            'product_name': content.product_name,
            'table_count': len(content.tables),
            'paragraph_count': len(content.paragraphs),
            'total_table_rows': sum(len(table.data) for table in content.tables),
            'total_text_length': sum(len(para.text) for para in content.paragraphs),
            'metadata_keys': list(content.metadata.keys()) if content.metadata else [],
            'table_types': table_types
        }
