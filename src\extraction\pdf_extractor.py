#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF内容提取器
使用pdfplumber提取PDF文档的文本、表格等内容
"""

import os
from typing import List, Optional
import pdfplumber
from .base_extractor import BaseExtractor, ExtractedContent, ExtractedTable, ExtractedParagraph
from ..utils import get_logger

logger = get_logger(__name__)


class PDFExtractor(BaseExtractor):
    """PDF内容提取器"""
    
    def __init__(self):
        super().__init__()
        self.logger.info("PDF提取器初始化完成")
    
    def get_supported_extensions(self) -> List[str]:
        """获取支持的文件扩展名"""
        return ['.pdf']
    
    def extract_content(self, file_path: str) -> Optional[ExtractedContent]:
        """
        提取PDF文件内容
        
        Args:
            file_path: PDF文件路径
            
        Returns:
            Optional[ExtractedContent]: 提取的内容
        """
        try:
            if not os.path.exists(file_path):
                self.logger.error(f"PDF文件不存在: {file_path}")
                return None
            
            self.logger.info(f"开始提取PDF内容: {os.path.basename(file_path)}")
            
            content = ExtractedContent()
            
            with pdfplumber.open(file_path) as pdf:
                # 提取元数据
                if pdf.metadata:
                    content.metadata = dict(pdf.metadata)
                    # 尝试从元数据获取标题
                    if 'Title' in content.metadata:
                        content.title = str(content.metadata['Title'])
                
                # 逐页提取内容
                for page_num, page in enumerate(pdf.pages, 1):
                    self.logger.debug(f"处理第 {page_num} 页")
                    
                    # 提取表格
                    tables = self._extract_tables_from_page(page, page_num)
                    # 标准化表格结构
                    standardized_tables = []
                    for table in tables:
                        standardized_table = self.standardize_table_structure(table)
                        standardized_tables.append(standardized_table)
                    content.tables.extend(standardized_tables)
                    
                    # 提取段落文本
                    paragraphs = self._extract_paragraphs_from_page(page, page_num)
                    content.paragraphs.extend(paragraphs)
                    
                    # 从第一页提取标题
                    if page_num == 1 and not content.title:
                        content.title = self._extract_title_from_page(page)

            # 合并相关表格（解决PDF表格分割问题）
            content.tables = self._merge_related_tables(content.tables)

            # 按优先级提取产品名称：页眉 → 文件名 → 产品陈述
            content.product_name = self._extract_product_name_with_priority(file_path, content)

            self.logger.info(f"PDF内容提取完成: {len(content.tables)} 个表格, {len(content.paragraphs)} 个段落")
            return content
            
        except Exception as e:
            self.logger.error(f"PDF内容提取失败 {file_path}: {e}")
            return None
    
    def _extract_tables_from_page(self, page, page_num: int) -> List[ExtractedTable]:
        """从页面提取表格"""
        tables = []
        
        try:
            # 使用pdfplumber提取表格
            page_tables = page.extract_tables()
            
            for table_idx, table_data in enumerate(page_tables):
                if not table_data:
                    continue
                
                # 清理表格数据
                cleaned_data = []
                headers = None
                
                for row_idx, row in enumerate(table_data):
                    if not row:
                        continue
                    
                    # 清理行数据
                    cleaned_row = [self._clean_text(str(cell)) if cell else "" for cell in row]
                    
                    # 检查是否为有效行
                    if self._is_valid_table_row(cleaned_row):
                        # 第一个有效行可能是表头
                        if not headers and row_idx == 0:
                            # 检查是否看起来像表头
                            if self._looks_like_header(cleaned_row):
                                headers = cleaned_row
                                continue
                        
                        cleaned_data.append(cleaned_row)
                
                if cleaned_data:
                    table = ExtractedTable(
                        page=page_num,
                        data=cleaned_data,
                        headers=headers
                    )
                    tables.append(table)
                    
                    self.logger.debug(f"第 {page_num} 页提取到表格 {table_idx + 1}: {len(cleaned_data)} 行")
        
        except Exception as e:
            self.logger.debug(f"第 {page_num} 页表格提取失败: {e}")
        
        return tables

    def _merge_related_tables(self, tables: List[ExtractedTable]) -> List[ExtractedTable]:
        """
        合并相关的表格（解决PDF表格分割问题）

        Args:
            tables: 原始表格列表

        Returns:
            List[ExtractedTable]: 合并后的表格列表
        """
        if len(tables) <= 1:
            return tables

        merged_tables = []
        i = 0

        while i < len(tables):
            current_table = tables[i]

            # 查找可以合并的后续表格
            merged_data = current_table.data.copy()
            merged_headers = current_table.headers
            j = i + 1

            while j < len(tables):
                next_table = tables[j]

                # 检查是否可以合并
                if self._can_merge_tables(current_table, next_table):
                    self.logger.debug(f"合并表格 {i+1} 和 {j+1}")

                    # 合并数据
                    merged_data.extend(next_table.data)

                    # 如果当前表格没有表头但下一个有，使用下一个的表头
                    if not merged_headers and next_table.headers:
                        merged_headers = next_table.headers

                    j += 1
                else:
                    break

            # 创建合并后的表格
            merged_table = ExtractedTable(
                page=current_table.page,
                data=merged_data,
                headers=merged_headers,
                table_type=current_table.table_type,
                confidence=current_table.confidence
            )

            merged_tables.append(merged_table)
            i = j

        self.logger.info(f"表格合并完成: {len(tables)} -> {len(merged_tables)}")
        return merged_tables

    def _can_merge_tables(self, table1: ExtractedTable, table2: ExtractedTable) -> bool:
        """
        判断两个表格是否可以合并

        Args:
            table1: 第一个表格
            table2: 第二个表格

        Returns:
            bool: 是否可以合并
        """
        # 必须在同一页
        if table1.page != table2.page:
            return False

        # 检查列数是否兼容
        if not table1.data or not table2.data:
            return False

        # 获取列数
        cols1 = len(table1.data[0]) if table1.data else 0
        cols2 = len(table2.data[0]) if table2.data else 0

        # 列数必须相同或相近（允许1列差异）
        if abs(cols1 - cols2) > 1:
            return False

        # 检查表格类型兼容性
        if table1.table_type and table2.table_type:
            # 如果都有类型，必须相同或其中一个是unknown
            if (table1.table_type != table2.table_type and
                table1.table_type != 'unknown' and
                table2.table_type != 'unknown'):
                return False

        # 检查内容连续性（简单的启发式规则）
        return self._check_content_continuity(table1, table2)

    def _check_content_continuity(self, table1: ExtractedTable, table2: ExtractedTable) -> bool:
        """检查表格内容的连续性"""
        # 如果第一个表格的最后一行和第二个表格的第一行看起来相关，则可以合并
        if not table1.data or not table2.data:
            return False

        # 检查是否为A/B组分相关表格
        all_text1 = ' '.join([' '.join(row) for row in table1.data[-2:]])  # 最后两行
        all_text2 = ' '.join([' '.join(row) for row in table2.data[:2]])   # 前两行

        # A/B组分关键词
        ab_keywords = ['109-17A', '109-17B', 'A组分', 'B组分', '混合', '固化', '硬度', '电阻']

        # 如果两个表格都包含A/B组分相关内容，可能可以合并
        has_ab1 = any(keyword in all_text1 for keyword in ab_keywords)
        has_ab2 = any(keyword in all_text2 for keyword in ab_keywords)

        if has_ab1 and has_ab2:
            return True

        # 检查参数名称的连续性
        param_keywords = ['外观', '粘度', '密度', '硬度', '电阻', '绝缘', '拉伸', '阻燃']
        has_param1 = any(keyword in all_text1 for keyword in param_keywords)
        has_param2 = any(keyword in all_text2 for keyword in param_keywords)

        return has_param1 and has_param2

    def _extract_paragraphs_from_page(self, page, page_num: int) -> List[ExtractedParagraph]:
        """从页面提取段落"""
        paragraphs = []
        
        try:
            # 提取页面文本
            text = page.extract_text()
            if not text:
                return paragraphs
            
            # 按行分割文本
            lines = text.split('\n')
            
            for line in lines:
                cleaned_line = self._clean_text(line)
                
                if self._is_valid_paragraph(cleaned_line):
                    paragraph = ExtractedParagraph(
                        page=page_num,
                        text=cleaned_line
                    )
                    paragraphs.append(paragraph)
        
        except Exception as e:
            self.logger.debug(f"第 {page_num} 页段落提取失败: {e}")
        
        return paragraphs
    
    def _extract_title_from_page(self, page) -> str:
        """从页面提取标题"""
        try:
            text = page.extract_text()
            if not text:
                return ""
            
            # 取前几行作为候选标题
            lines = text.split('\n')[:5]
            
            for line in lines:
                cleaned_line = self._clean_text(line)
                # 标题通常较短且不包含特殊字符
                if cleaned_line and 5 <= len(cleaned_line) <= 50:
                    # 排除明显不是标题的内容
                    if not any(keyword in cleaned_line.lower() for keyword in ['page', '页', 'table', '表']):
                        return cleaned_line
            
            return ""
            
        except Exception as e:
            self.logger.debug(f"标题提取失败: {e}")
            return ""

    def _extract_product_name_with_priority(self, file_path: str, content) -> str:
        """按优先级提取产品名称：页眉 → 文件名 → 产品陈述"""

        # 优先级1：从页眉提取（PDF中通常在页面顶部）
        product_name = self._extract_product_name_from_header(content)
        if product_name:
            self.logger.debug(f"从页眉提取产品名称: {product_name}")
            return product_name

        # 优先级2：从文件名提取
        product_name = self._extract_product_name_from_filename(os.path.basename(file_path))
        if product_name and product_name != "未知产品":
            self.logger.debug(f"从文件名提取产品名称: {product_name}")
            return product_name

        # 优先级3：从产品陈述提取
        product_name = self._extract_product_name_from_statement(content)
        if product_name:
            self.logger.debug(f"从产品陈述提取产品名称: {product_name}")
            return product_name

        # 如果都没有，返回文件名（去掉扩展名）
        return os.path.splitext(os.path.basename(file_path))[0]

    def _extract_product_name_from_header(self, content) -> str:
        """从页眉提取产品名称（PDF中通常是页面顶部的文本）"""
        try:
            # 查找第一页的前几个段落（通常是页眉位置）
            for para in content.paragraphs[:5]:  # 只检查前5个段落
                if para.page == 1:  # 只检查第一页
                    text = para.text.strip()
                    if text and len(text) < 100:  # 页眉通常较短

                        # 方法1：尝试从标签格式提取（产品名称：XXX）
                        product_name = self._extract_from_label_format(text)
                        if product_name:
                            return product_name

                        # 方法2：直接从页眉文本提取产品型号
                        product_name = self._parse_product_name_from_text(text)
                        if product_name:
                            return product_name
        except Exception as e:
            self.logger.debug(f"页眉产品名称提取失败: {e}")

        return ""

    def _extract_from_label_format(self, text: str) -> str:
        """从标签格式提取产品名称（如：产品名称：FM-102N-2）"""
        import re

        # 定义各种可能的标签词
        label_variants = [
            '产品名称', '产品名字', '产品名',
            '型号', '产品型号', '产品编号',
            'Product Name', 'Product', 'Model'
        ]

        # 定义各种可能的分隔符
        separators = ['：', ':', '=', '－', '-']

        for label in label_variants:
            for sep in separators:
                # 构建匹配模式
                pattern = rf'{re.escape(label)}\s*{re.escape(sep)}\s*([^\s,，。；\n]+)'
                match = re.search(pattern, text, re.IGNORECASE)
                if match:
                    product_name = match.group(1).strip()
                    if self._is_valid_product_name(product_name):
                        self.logger.debug(f"从标签格式提取: {label}{sep}{product_name}")
                        return product_name

        return ""

    def _extract_product_name_from_statement(self, content) -> str:
        """从产品陈述提取产品名称"""
        try:
            # 查找产品陈述相关的表格
            for table in content.tables:
                for row in table.data:
                    if len(row) >= 2:
                        first_cell = row[0].strip()
                        if any(keyword in first_cell for keyword in ['产品陈述', '产品描述', '产品介绍']):
                            statement_text = row[1]
                            product_name = self._parse_product_name_from_text(statement_text)
                            if product_name:
                                return product_name

            # 从段落中查找产品陈述
            for para in content.paragraphs:
                if any(keyword in para.text for keyword in ['产品陈述', '产品描述', '产品介绍']):
                    product_name = self._parse_product_name_from_text(para.text)
                    if product_name:
                        return product_name

        except Exception as e:
            self.logger.debug(f"产品陈述产品名称提取失败: {e}")

        return ""

    def _parse_product_name_from_text(self, text: str) -> str:
        """从文本中解析产品名称"""
        import re

        # 产品名称模式（优先匹配更具体的模式）
        patterns = [
            r'(FM-[A-Z0-9]+(?:-[A-Z0-9]+)*)',  # FM-109-17, FM-3602-12 等
            r'([A-Z]{2,}-[0-9]+(?:-[A-Z0-9]+)*)',  # 通用格式：字母-数字-可选后缀
            r'([A-Z]+[0-9]+[A-Z]*)',  # ABC123, FM3602 等
            r'(产品型号[：:]\s*([^\s,，。；]+))',  # 产品型号：XXX
            r'(型号[：:]\s*([^\s,，。；]+))',  # 型号：XXX
        ]

        for pattern in patterns:
            matches = re.findall(pattern, text)
            if matches:
                # 取第一个匹配结果
                match = matches[0]
                if isinstance(match, tuple):
                    # 如果是元组，取最后一个非空元素
                    for item in reversed(match):
                        if item and item.strip():
                            product_name = item.strip()
                            # 验证产品名称的合理性
                            if self._is_valid_product_name(product_name):
                                return product_name
                else:
                    product_name = match.strip()
                    if self._is_valid_product_name(product_name):
                        return product_name

        return ""

    def _is_valid_product_name(self, name: str) -> bool:
        """验证产品名称的合理性"""
        if not name or len(name) < 2:
            return False

        # 排除明显不是产品名称的文本
        invalid_keywords = [
            '技术参数', '参数表', '数据表', '说明书', '手册',
            '工艺', '性能', '测试', '检验', '标准',
            '公司', '有限', '股份', '集团'
        ]

        for keyword in invalid_keywords:
            if keyword in name:
                return False

        # 产品名称通常包含字母和数字
        import re
        if re.search(r'[A-Za-z]', name) and re.search(r'[0-9]', name):
            return True

        # 或者是纯字母但长度合适
        if re.match(r'^[A-Za-z]{2,10}$', name):
            return True

        return False

    def _extract_product_name_from_page(self, page) -> str:
        """从页面提取产品名称"""
        try:
            text = page.extract_text()
            if not text:
                return ""
            
            # 使用基类方法尝试提取产品名称
            product_name = self._extract_product_name_from_text(text)
            if product_name:
                return product_name
            
            # 尝试其他PDF特有的模式
            import re
            
            # 查找型号模式
            model_patterns = [
                r'([A-Z]{2,}\-[0-9]+)',  # 如 FM-2000
                r'([A-Z]+[0-9]+[A-Z]*)',  # 如 ABC123
            ]
            
            for pattern in model_patterns:
                match = re.search(pattern, text)
                if match:
                    return match.group(1)
            
            return ""
            
        except Exception as e:
            self.logger.debug(f"产品名称提取失败: {e}")
            return ""
    
    def _extract_product_name_from_filename(self, filename: str) -> str:
        """从文件名提取产品名称"""
        import re

        # 移除扩展名
        name = os.path.splitext(filename)[0]

        # 先尝试从文件名中提取产品型号
        product_name = self._parse_product_name_from_text(name)
        if product_name:
            return product_name

        # 如果没有找到，移除常见后缀后再试
        suffixes = ['技术参数表', '参数表', '技术参数', 'TDS', 'datasheet', '数据表', '说明书']
        cleaned_name = name
        for suffix in suffixes:
            cleaned_name = re.sub(rf'{suffix}.*$', '', cleaned_name, flags=re.IGNORECASE)

        # 移除日期后缀（如20250101）
        cleaned_name = re.sub(r'\d{8}$', '', cleaned_name)
        cleaned_name = re.sub(r'\d{6}$', '', cleaned_name)

        # 移除括号内容
        cleaned_name = re.sub(r'\([^)]*\)$', '', cleaned_name)
        cleaned_name = re.sub(r'（[^）]*）$', '', cleaned_name)

        cleaned_name = cleaned_name.strip()

        # 再次尝试提取产品名称
        product_name = self._parse_product_name_from_text(cleaned_name)
        if product_name:
            return product_name

        # 如果还是没有，返回清理后的名称
        return cleaned_name if cleaned_name else "未知产品"
    
    def _looks_like_header(self, row: List[str]) -> bool:
        """判断行是否看起来像表头"""
        if not row:
            return False
        
        # 表头通常包含这些关键词
        header_keywords = [
            '项目', '参数', '指标', '性能', '数值', '单位', '值',
            'item', 'parameter', 'property', 'value', 'unit'
        ]
        
        row_text = ' '.join(row).lower()
        return any(keyword in row_text for keyword in header_keywords)
    
    def extract_text_only(self, file_path: str) -> str:
        """
        仅提取PDF的纯文本内容
        
        Args:
            file_path: PDF文件路径
            
        Returns:
            str: 提取的文本内容
        """
        try:
            text_parts = []
            
            with pdfplumber.open(file_path) as pdf:
                for page in pdf.pages:
                    text = page.extract_text()
                    if text:
                        text_parts.append(text)
            
            return '\n'.join(text_parts)
            
        except Exception as e:
            self.logger.error(f"PDF文本提取失败 {file_path}: {e}")
            return ""
    
    def extract_tables_only(self, file_path: str) -> List[List[List[str]]]:
        """
        仅提取PDF的表格内容
        
        Args:
            file_path: PDF文件路径
            
        Returns:
            List[List[List[str]]]: 提取的表格数据
        """
        try:
            all_tables = []
            
            with pdfplumber.open(file_path) as pdf:
                for page in pdf.pages:
                    tables = page.extract_tables()
                    for table in tables:
                        if table:
                            all_tables.append(table)
            
            return all_tables
            
        except Exception as e:
            self.logger.error(f"PDF表格提取失败 {file_path}: {e}")
            return []
