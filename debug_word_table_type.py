#!/usr/bin/env python3
"""调试Word文档表格类型识别"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.extraction.docx_extractor import DocxExtractor

def debug_word_table_type():
    """调试Word文档表格类型识别"""
    extractor = DocxExtractor()
    
    # 提取Word文档
    file_path = "./PDF/FM-102-1 技术参数表.docx"
    content = extractor.extract_content(file_path)
    
    if not content:
        print("❌ 无法提取Word文档内容")
        return
    
    print(f"📄 Word文档表格数量: {len(content.tables)}")
    
    for i, table in enumerate(content.tables):
        print(f"\n📊 表格 {i+1}:")
        print(f"  表格类型: {table.table_type}")
        print(f"  表头: {table.headers}")
        print(f"  数据行数: {len(table.data)}")
        
        if table.data:
            print("  前3行数据:")
            for j, row in enumerate(table.data[:3]):
                print(f"    行{j+1}: {row}")
        
        # 检查表格类型识别逻辑
        print(f"  表格类型识别详情:")
        
        # 检查表头
        if table.headers:
            header_text = ' '.join(table.headers).lower()
            print(f"    表头文本: '{header_text}'")
            
            # 检查A/B组分关键词
            ab_keywords = ['109-17a', '109-17b', 'a组分', 'b组分', 'a剂', 'b剂', '主剂', '固化剂']
            found_keywords = [kw for kw in ab_keywords if kw in header_text]
            print(f"    找到的A/B关键词: {found_keywords}")
        
        # 检查数据内容
        all_text = []
        for row in table.data[:5]:
            all_text.extend(row)
        content_text = ' '.join(all_text).lower()
        print(f"    内容文本: '{content_text[:100]}...'")
        
        ab_keywords = ['109-17a', '109-17b', 'a组分', 'b组分', 'a剂', 'b剂', '主剂', '固化剂']
        found_content_keywords = [kw for kw in ab_keywords if kw in content_text]
        print(f"    内容中找到的A/B关键词: {found_content_keywords}")

if __name__ == "__main__":
    debug_word_table_type()
