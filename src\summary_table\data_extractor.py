#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
技术数据提取器
从PDF和Word文档中提取技术参数数据
"""

import os
import re
from pathlib import Path
from typing import List, Dict, Any, Optional
from ..utils import get_logger
from ..extraction import PDFExtractor, DocxExtractor
from ..validation import DataValidator
from .parameter_mapper import ParameterMapper
from .ab_component_extractor import ABComponentExtractor

logger = get_logger(__name__)


class TechnicalDataExtractor:
    """技术数据提取器"""
    
    def __init__(self, config):
        self.config = config
        self.logger = get_logger(__name__)

        # 初始化提取器和参数映射器
        try:
            self.pdf_extractor = PDFExtractor()
            self.docx_extractor = DocxExtractor()
            self.parameter_mapper = ParameterMapper(config)
            self.ab_extractor = ABComponentExtractor()
            self.data_validator = DataValidator()
            self.logger.info("技术数据提取器初始化完成")
        except Exception as e:
            self.logger.error(f"提取器初始化失败: {e}")
            raise
    
    def extract_from_file(self, file_path: str) -> Optional[Dict[str, Any]]:
        """从文件中提取数据"""
        try:
            file_path = Path(file_path)
            
            if not file_path.exists():
                self.logger.error(f"文件不存在: {file_path}")
                return None
            
            self.logger.info(f"开始提取文件: {file_path.name}")
            
            # 根据文件类型选择提取器
            if file_path.suffix.lower() == '.pdf':
                return self._extract_from_pdf(str(file_path))
            elif file_path.suffix.lower() == '.docx':
                return self._extract_from_docx(str(file_path))
            else:
                self.logger.warning(f"不支持的文件格式: {file_path.suffix}")
                return None
                
        except Exception as e:
            self.logger.error(f"文件提取失败 {file_path}: {e}")
            return None
    
    def _extract_from_pdf(self, file_path: str) -> Optional[Dict[str, Any]]:
        """从PDF文件提取数据"""
        try:
            # 使用统一的PDF提取器
            extracted_content = self.pdf_extractor.extract_content(file_path)

            if not extracted_content:
                self.logger.warning(f"PDF文件内容为空: {file_path}")
                return None

            # 转换为旧格式以兼容现有代码
            content = self._convert_extracted_content_to_dict(extracted_content)

            # 提取产品信息和参数
            product_info = self._extract_product_info(content, file_path)
            raw_parameters = self._extract_parameters_from_content(content)

            if not raw_parameters:
                self.logger.warning(f"未提取到有效参数: {file_path}")
                return None

            # 使用参数映射器处理参数
            mapped_parameters = self.parameter_mapper.map_parameters(
                raw_parameters,
                product_info.get('product_name', '')
            )

            # 验证参数
            validated_parameters = self.parameter_mapper.validate_mapped_parameters(mapped_parameters)

            result = {
                'product_name': product_info.get('product_name', ''),
                'product_model': product_info.get('product_model', ''),
                'parameters': validated_parameters,
                'source_file': file_path,
                'file_type': 'pdf',
                'raw_content': content
            }

            # 数据验证和修复
            result = self.data_validator.validate_and_fix(result)

            self.logger.info(f"PDF提取完成: {len(result['parameters'])} 个参数")
            return result

        except Exception as e:
            self.logger.error(f"PDF提取失败: {e}")
            return None
    
    def _extract_from_docx(self, file_path: str) -> Optional[Dict[str, Any]]:
        """从Word文件提取数据"""
        try:
            # 使用统一的Word提取器
            extracted_content = self.docx_extractor.extract_content(file_path)

            if not extracted_content:
                self.logger.warning(f"Word文件内容为空: {file_path}")
                return None

            # 转换为旧格式以兼容现有代码
            content = self._convert_extracted_content_to_dict(extracted_content)

            # 提取产品信息和参数
            product_info = self._extract_product_info(content, file_path)
            raw_parameters = self._extract_parameters_from_content(content)

            if not raw_parameters:
                self.logger.warning(f"未提取到有效参数: {file_path}")
                return None

            # 使用参数映射器处理参数
            mapped_parameters = self.parameter_mapper.map_parameters(
                raw_parameters,
                product_info.get('product_name', '')
            )

            # 验证参数
            validated_parameters = self.parameter_mapper.validate_mapped_parameters(mapped_parameters)

            result = {
                'product_name': product_info.get('product_name', ''),
                'product_model': product_info.get('product_model', ''),
                'parameters': validated_parameters,
                'source_file': file_path,
                'file_type': 'docx',
                'raw_content': content
            }

            # 数据验证和修复
            result = self.data_validator.validate_and_fix(result)

            self.logger.info(f"Word提取完成: {len(result['parameters'])} 个参数")
            return result

        except Exception as e:
            self.logger.error(f"Word提取失败: {e}")
            return None

    def _convert_extracted_content_to_dict(self, extracted_content) -> Dict[str, Any]:
        """将新的ExtractedContent对象转换为旧的字典格式"""
        try:
            # 转换表格
            tables = []
            for table in extracted_content.tables:
                table_dict = {
                    'data': table.data,
                    'headers': table.headers,
                    'page': table.page
                }
                tables.append(table_dict)

            # 转换段落
            paragraphs = []
            for para in extracted_content.paragraphs:
                para_dict = {
                    'text': para.text,
                    'page': para.page,
                    'style': para.style
                }
                paragraphs.append(para_dict)

            return {
                'title': extracted_content.title,
                'product_name': extracted_content.product_name,
                'tables': tables,
                'paragraphs': paragraphs,
                'metadata': extracted_content.metadata or {}
            }

        except Exception as e:
            self.logger.error(f"内容格式转换失败: {e}")
            return {}

    def _extract_product_info(self, content: Dict[str, Any], file_path: str) -> Dict[str, str]:
        """提取产品信息"""
        product_info = {
            'product_name': '',
            'product_model': ''
        }
        
        try:
            # 从内容中提取产品名称
            product_name = content.get('product_name', '')
            
            # 如果内容中没有产品名称，从文件名提取
            if not product_name:
                filename = os.path.basename(file_path)
                # 移除扩展名和常见后缀
                product_name = filename.replace('.pdf', '').replace('.docx', '')
                product_name = re.sub(r'(技术参数表|参数表|技术参数|参数|TDS)', '', product_name).strip()
            
            product_info['product_name'] = product_name or "未知产品"
            
            # 尝试提取产品型号
            product_model = self._extract_product_model(content, product_name)
            product_info['product_model'] = product_model
            
            return product_info
            
        except Exception as e:
            self.logger.error(f"产品信息提取失败: {e}")
            return product_info
    
    def _extract_product_model(self, content: Dict[str, Any], product_name: str) -> str:
        """提取产品型号"""
        try:
            # 从段落中查找型号模式
            paragraphs = content.get('paragraphs', [])
            
            model_patterns = [
                r'型号[：:]\s*([A-Z0-9\-]+)',
                r'产品型号[：:]\s*([A-Z0-9\-]+)',
                r'Model[：:]\s*([A-Z0-9\-]+)',
                r'([A-Z]{2,}\-[0-9]+)',  # 如 FM-2000
                r'([A-Z]+[0-9]+)',       # 如 ABC123
            ]
            
            for para in paragraphs:
                text = para.get('text', '')
                for pattern in model_patterns:
                    match = re.search(pattern, text, re.IGNORECASE)
                    if match:
                        return match.group(1).strip()
            
            # 从产品名称中提取型号
            for pattern in model_patterns[3:]:  # 只使用通用模式
                match = re.search(pattern, product_name)
                if match:
                    return match.group(1).strip()
            
            return ""
            
        except Exception as e:
            self.logger.debug(f"产品型号提取失败: {e}")
            return ""
    
    def _extract_parameters_from_content(self, content: Dict[str, Any]) -> Dict[str, str]:
        """从内容中提取参数"""
        parameters = {}

        try:
            # 优先使用A/B组分提取器
            ab_parameters = self.ab_extractor.extract_ab_components(content)
            if ab_parameters:
                parameters.update(ab_parameters)
                self.logger.info(f"A/B组分提取器提取到 {len(ab_parameters)} 个参数")

            # 从表格中提取其他参数
            tables = content.get('tables', [])
            for table in tables:
                # 检查是否为A/B组分表格
                is_ab_table = ab_parameters and self.ab_extractor._is_ab_component_table(table)

                if is_ab_table:
                    # 对于A/B组分表格，只提取非A/B组分的参数（如工艺性能等）
                    table_params = self._extract_non_ab_parameters_from_table(table)
                    self.logger.debug(f"从A/B组分表格提取非A/B参数: {len(table_params)} 个")
                else:
                    # 对于普通表格，正常提取所有参数
                    table_params = self._extract_parameters_from_table(table)
                    self.logger.debug(f"从普通表格提取参数: {len(table_params)} 个")

                # 避免覆盖A/B组分参数
                for key, value in table_params.items():
                    if key not in parameters:
                        parameters[key] = value

            # 从段落中提取参数
            paragraphs = content.get('paragraphs', [])
            for para in paragraphs:
                para_params = self._extract_parameters_from_paragraph(para)
                # 避免覆盖已有参数
                for key, value in para_params.items():
                    if key not in parameters:
                        parameters[key] = value

            self.logger.debug(f"总共提取到 {len(parameters)} 个原始参数")
            return parameters

        except Exception as e:
            self.logger.error(f"参数提取失败: {e}")
            return {}

    def _extract_non_ab_parameters_from_table(self, table: Dict[str, Any]) -> Dict[str, str]:
        """从A/B组分表格中提取非A/B组分的参数（如工艺性能、固化物性能等）"""
        parameters = {}
        table_data = table.get('data', [])

        if not table_data:
            return parameters

        # 首先检查表头是否包含产品描述
        table_headers = table.get('headers', [])
        if table_headers and len(table_headers) >= 2:
            header_name = table_headers[0].strip()
            product_description_keywords = ['产品陈述', '产品描述', '产品介绍', '产品特点', '性能特点']
            if any(keyword in header_name for keyword in product_description_keywords):
                header_value = table_headers[1].strip()
                if header_value:
                    parameters['产品描述'] = header_value
                    self.logger.debug(f"从表头提取产品描述: {header_value[:50]}...")

        # 查找A/B列的位置
        ab_columns = self.ab_extractor._find_ab_columns(table_data)
        if not ab_columns:
            return parameters

        a_col, b_col = ab_columns

        # 遍历表格行，查找非A/B组分的参数
        for row in table_data:
            if len(row) < 2:
                continue

            item_name = row[0].strip()
            if not item_name:
                continue

            # 跳过表头行
            if item_name in ['测试项目', '项目', 'Item', 'Parameter']:
                continue

            # 跳过A/B组分的核心参数（这些已经被A/B组分提取器处理了）
            # 但不跳过混合类参数（如混合粘度、混合密度等）
            ab_core_params = ['外观', '粘度', '密度', '保存期限']
            is_ab_core_param = False
            for param in ab_core_params:
                if param in item_name and '混合' not in item_name:
                    is_ab_core_param = True
                    break

            if is_ab_core_param:
                continue

            # 特殊处理：产品描述/产品陈述
            product_description_keywords = ['产品陈述', '产品描述', '产品介绍', '产品特点', '性能特点']
            if any(keyword in item_name for keyword in product_description_keywords):
                if len(row) >= 2:
                    value = row[1].strip()  # 产品描述在第2列
                    if value:
                        parameters['产品描述'] = value
                        self.logger.debug(f"提取产品描述: {value[:50]}...")
                continue

            # 使用TDS表格结构识别来确定数据列
            column_structure = self.ab_extractor._identify_tds_table_structure(table)
            if column_structure:
                # 对于非A/B组分表格，使用第3列作为数据列
                value_col = column_structure['data_a']  # 第3列（索引2）

                # 特殊处理：如果有多个产品列（慢速、中速、快速），选择第一个有数据的列
                if len(row) >= 5:
                    # 检查是否为多速度产品表格
                    for col_idx in range(2, min(5, len(row))):
                        test_value = row[col_idx].strip()
                        if test_value and not self._is_unit_or_condition(test_value):
                            value_col = col_idx
                            break

                if value_col < len(row):
                    value = row[value_col].strip()
                    if value and not self._is_unit_or_condition(value):
                        parameters[item_name] = value
                        self.logger.debug(f"提取非A/B参数: {item_name} = {value} (列{value_col})")
            else:
                # 备用方案：默认使用第3列
                if len(row) > 2:
                    value = row[2].strip()
                    if value and not self._is_unit_or_condition(value):
                        parameters[item_name] = value
                        self.logger.debug(f"提取非A/B参数: {item_name} = {value} (默认列2)")

        return parameters

    def _extract_parameters_from_table(self, table: Dict[str, Any]) -> Dict[str, str]:
        """从表格中提取参数"""
        parameters = {}

        try:
            table_data = table.get('data', [])

            # 识别表格结构
            column_structure = self._identify_table_structure(table)

            for row in table_data:
                if len(row) < 3:  # 至少需要3列（项目、单位、数据）
                    continue

                key = str(row[0]).strip()

                # 跳过表头行
                if key in ['测试项目', '项目', 'Item', 'Parameter']:
                    continue

                # 根据表格结构提取数据
                if column_structure['data_col'] < len(row):
                    value = str(row[column_structure['data_col']]).strip()

                    # 过滤无效的键值对
                    if self._is_valid_parameter_pair(key, value):
                        parameters[key] = value

            return parameters

        except Exception as e:
            self.logger.debug(f"表格参数提取失败: {e}")
            return {}

    def _identify_table_structure(self, table: Dict[str, Any]) -> Dict[str, int]:
        """
        识别表格结构，确定数据列位置

        标准结构：
        - 第1列：测试项目
        - 第2列：单位或条件
        - 第3列：数据值
        - 第4列：标准（可选）
        """
        table_data = table.get('data', [])
        headers = table.get('headers', [])

        # 默认结构
        structure = {
            'item_col': 0,    # 项目列
            'unit_col': 1,    # 单位列
            'data_col': 2,    # 数据列
            'standard_col': 3 # 标准列
        }

        # 分析第一行或表头来确定结构
        first_row = headers if headers else table_data[0] if table_data else []

        if len(first_row) >= 4:
            # 检查第4列是否为标准列
            fourth_col = str(first_row[3]).strip().lower()
            if any(keyword in fourth_col for keyword in ['标准', 'standard', '执行标准']):
                # 标准4列结构
                structure['data_col'] = 2
                structure['standard_col'] = 3
            else:
                # 可能是5列结构（包含A/B组分）
                if len(first_row) >= 5:
                    structure['data_col'] = 2  # 默认取第3列作为主要数据
                    structure['standard_col'] = 4

        return structure

    def _is_unit_or_condition(self, text: str) -> bool:
        """判断文本是否为单位或测试条件"""
        if not text:
            return True

        # 如果包含数值，很可能是实际数据而不是单位
        import re
        if re.search(r'\d+[±\-\+]?\d*', text):
            # 包含数字，检查是否为数值数据
            if any(pattern in text for pattern in ['±', '>', '<', '≥', '≤', ':', '/']):
                return False  # 包含数值范围符号，是数据
            if re.match(r'^\d+(\.\d+)?[±\-\+]\d+(\.\d+)?$', text):
                return False  # 纯数值格式，如 400±200

        # 常见的单位和条件标识
        unit_indicators = [
            'mPa·s', 'Pa·s', 'g/cm3', 'g/ml', 'kg/m3',
            'Shore A', 'Shore D', 'MPa', 'KV/mm', 'Ω.cm',
            '℃', '°C', 'min', 'h', 'hr', '%', 'mm',
            '目测', '室温', '密封', '常温', '常湿',
            '23℃', '25℃', '60℃', '85℃',
            'GB/T', 'ASTM', 'ISO', '企业标准',
            '单位或条件', '测试条件', '执行标准',
            '重量比', '慢速产品', '中速产品', '快速产品'
        ]

        # 检查是否包含单位标识
        for indicator in unit_indicators:
            if indicator in text:
                return True

        # 检查是否为表头标识
        header_indicators = ['109-17A', '109-17B', '测试项目', '单位或条件', '执行标准']
        if text in header_indicators:
            return True

        return False

    def _extract_parameters_from_paragraph(self, paragraph: Dict[str, Any]) -> Dict[str, str]:
        """从段落中提取参数"""
        parameters = {}
        
        try:
            text = paragraph.get('text', '')
            
            # 查找键值对模式
            patterns = [
                r'([^：:]+)[：:]\s*([^，,；;\n]+)',  # 冒号分隔
                r'([^=]+)=\s*([^，,；;\n]+)',      # 等号分隔
            ]
            
            for pattern in patterns:
                matches = re.finditer(pattern, text)
                for match in matches:
                    key = match.group(1).strip()
                    value = match.group(2).strip()
                    
                    if self._is_valid_parameter_pair(key, value):
                        parameters[key] = value
            
            return parameters
            
        except Exception as e:
            self.logger.debug(f"段落参数提取失败: {e}")
            return {}
    
    def _is_valid_parameter_pair(self, key: str, value: str) -> bool:
        """验证键值对是否有效"""
        if not key or not value:
            return False
        
        # 过滤掉明显无效的键值对
        invalid_keys = [
            '序号', '编号', 'No', 'NO', '项目', '参数', '单位', 'Unit',
            '备注', '说明', 'Note', 'Remark', '表', 'Table'
        ]
        
        if key in invalid_keys or key == value:
            return False
        
        # 键不能太长
        if len(key) > 50:
            return False
        
        # 值不能为空或只包含特殊字符
        if not re.search(r'[a-zA-Z0-9\u4e00-\u9fff]', value):
            return False
        
        return True
    
    def scan_folder(self, folder_path: str) -> List[str]:
        """扫描文件夹中的支持文件"""
        try:
            folder = Path(folder_path)
            if not folder.exists():
                self.logger.error(f"文件夹不存在: {folder_path}")
                return []
            
            supported_files = []
            
            # 扫描PDF和Word文件
            for pattern in ['*.pdf', '*.docx']:
                files = list(folder.rglob(pattern))
                supported_files.extend([str(f) for f in files])
            
            # 过滤临时文件和隐藏文件
            supported_files = [
                f for f in supported_files 
                if not os.path.basename(f).startswith(('~$', '.'))
            ]
            
            self.logger.info(f"找到 {len(supported_files)} 个支持的文件")
            return supported_files
            
        except Exception as e:
            self.logger.error(f"文件夹扫描失败: {e}")
            return []
    
    def extract_batch(self, file_paths: List[str]) -> List[Dict[str, Any]]:
        """批量提取文件数据"""
        results = []
        
        for file_path in file_paths:
            try:
                result = self.extract_from_file(file_path)
                if result:
                    results.append(result)
                else:
                    self.logger.warning(f"文件提取失败: {file_path}")
            except Exception as e:
                self.logger.error(f"批量提取出错 {file_path}: {e}")
        
        self.logger.info(f"批量提取完成: {len(results)}/{len(file_paths)} 成功")
        return results
