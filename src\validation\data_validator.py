#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据验证模块
检查数据完整性、质量和异常值
"""

import re
from typing import Dict, List, Tuple, Any, Optional
from dataclasses import dataclass
from ..utils import get_logger

logger = get_logger(__name__)

@dataclass
class ValidationResult:
    """验证结果"""
    filename: str
    product_name: str
    total_params: int
    missing_params: List[str]
    invalid_params: List[Tuple[str, str, str]]  # (参数名, 值, 原因)
    warnings: List[str]
    quality_score: float
    
    @property
    def is_valid(self) -> bool:
        """是否有效"""
        return len(self.invalid_params) == 0
    
    @property
    def completion_rate(self) -> float:
        """完成率"""
        if self.total_params == 0:
            return 0.0
        return (self.total_params - len(self.missing_params)) / self.total_params * 100

class DataValidator:
    """数据验证器"""
    
    def __init__(self):
        # 参数值的验证规则
        self.validation_rules = {
            # 数值范围验证
            '粘度': {'type': 'numeric', 'min': 0, 'max': 1000000, 'unit': 'mPa·s'},
            '密度': {'type': 'numeric', 'min': 0.1, 'max': 10.0, 'unit': 'g/cm3'},
            '硬度': {'type': 'numeric', 'min': 0, 'max': 100, 'unit': 'Shore'},
            '体积电阻率': {'type': 'scientific', 'min': 1e6, 'max': 1e20, 'unit': 'Ω·cm'},
            '绝缘强度': {'type': 'numeric', 'min': 0, 'max': 100, 'unit': 'KV/mm'},
            '介电常数': {'type': 'numeric', 'min': 1, 'max': 100, 'unit': ''},
            '导热系数': {'type': 'numeric', 'min': 0, 'max': 10, 'unit': 'W/(m·K)'},
            '吸水率': {'type': 'percentage', 'min': 0, 'max': 100, 'unit': '%'},
            
            # 文本格式验证
            '外观': {'type': 'text', 'required_words': ['液体', '透明', '黄色', '黑色', '白色']},
            '混合比例': {'type': 'ratio', 'pattern': r'\d+:\d+'},
            '阻燃': {'type': 'grade', 'valid_values': ['V-0', 'V-1', 'V-2', 'HB', 'UL-94']},
            '应用温度': {'type': 'temperature_range', 'pattern': r'-?\d+~\d+'},
        }
        
        # 必需参数列表
        self.required_params = [
            '外观(A)', '外观(B)', '粘度(A)', '粘度(B)', '密度(A)', '密度(B)',
            '保存期限(A)', '保存期限(B)', '混合比例', '混合粘度', '硬度',
            '体积电阻率', '绝缘强度'
        ]
        
        # 重要参数列表（影响质量评分）
        self.important_params = [
            '混合比例', '固化时间', '硬度', '体积电阻率', '绝缘强度',
            '应用温度', '性能特点'
        ]

        # 常见的错误模式
        self.error_patterns = {
            'unit_as_value': [
                r'^[a-zA-Z·℃%]+$',  # 纯单位
                r'^\w+/\w+$',       # 比率单位
                r'^重量比$',         # 重量比
                r'^目测$',           # 目测
            ],
            'empty_or_invalid': [
                r'^$',              # 空值
                r'^-+$',            # 横线
                r'^N/A$',           # N/A
                r'^未知$',           # 未知
            ]
        }
    
    def validate_product_data(self, filename: str, product_name: str, 
                            tech_params: Dict[str, Any]) -> ValidationResult:
        """验证产品数据"""
        logger.debug(f"开始验证产品数据: {product_name}")
        
        missing_params = []
        invalid_params = []
        warnings = []
        
        # 检查必需参数
        for param in self.required_params:
            if param not in tech_params or not tech_params[param]:
                missing_params.append(param)
        
        # 验证参数值
        for param, value in tech_params.items():
            if not value or str(value).strip() == "":
                continue
                
            validation_result = self._validate_parameter_value(param, value)
            if not validation_result['valid']:
                invalid_params.append((param, str(value), validation_result['reason']))
            
            if validation_result.get('warning'):
                warnings.append(f"{param}: {validation_result['warning']}")
        
        # 计算质量评分
        quality_score = self._calculate_quality_score(
            tech_params, missing_params, invalid_params
        )
        
        result = ValidationResult(
            filename=filename,
            product_name=product_name,
            total_params=len(self.required_params),
            missing_params=missing_params,
            invalid_params=invalid_params,
            warnings=warnings,
            quality_score=quality_score
        )
        
        logger.debug(f"验证完成: {product_name} - 质量评分: {quality_score:.1f}")
        return result

    def validate_and_fix(self, extracted_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        验证和修复提取的数据

        Args:
            extracted_data: 提取的原始数据

        Returns:
            Dict[str, Any]: 修复后的数据
        """
        try:
            fixed_data = extracted_data.copy()

            # 验证和修复参数
            if 'parameters' in fixed_data:
                fixed_data['parameters'] = self._validate_and_fix_parameters(fixed_data['parameters'])

            # 验证A/B组分一致性
            fixed_data = self._validate_ab_consistency(fixed_data)

            # 验证数据完整性
            fixed_data = self._validate_data_completeness(fixed_data)

            logger.info("数据验证和修复完成")
            return fixed_data

        except Exception as e:
            logger.error(f"数据验证失败: {e}")
            return extracted_data

    def _validate_and_fix_parameters(self, parameters: Dict[str, str]) -> Dict[str, str]:
        """验证和修复参数"""
        fixed_params = {}

        for param_name, param_value in parameters.items():
            # 检查是否为错误的单位值
            if self._is_error_value(param_value):
                # 尝试修复
                fixed_value = self._fix_parameter_value(param_name, param_value, parameters)
                if fixed_value:
                    fixed_params[param_name] = fixed_value
                    logger.info(f"修复参数值: {param_name} = {param_value} -> {fixed_value}")
                else:
                    logger.warning(f"无法修复参数: {param_name} = {param_value}")
            else:
                # 验证值的格式
                if self._validate_parameter_format(param_name, param_value):
                    fixed_params[param_name] = param_value
                else:
                    logger.warning(f"参数格式异常: {param_name} = {param_value}")
                    fixed_params[param_name] = param_value  # 保留原值

        return fixed_params

    def _is_error_value(self, value: str) -> bool:
        """检查是否为错误值"""
        for error_type, patterns in self.error_patterns.items():
            for pattern in patterns:
                if re.match(pattern, value.strip()):
                    return True
        return False

    def _fix_parameter_value(self, param_name: str, error_value: str, all_params: Dict[str, str]) -> Optional[str]:
        """尝试修复参数值"""
        # 策略1：查找相似参数名的正确值
        for other_name, other_value in all_params.items():
            if (param_name.replace('(A)', '').replace('(B)', '') in other_name or
                other_name.replace('(A)', '').replace('(B)', '') in param_name):
                if not self._is_error_value(other_value):
                    return other_value

        # 策略2：基于参数名推断默认值
        default_values = {
            '混合比例': '100:100',
            '外观': '透明液体',
            '阻燃': 'V-0',
        }

        base_param = param_name.replace('(A)', '').replace('(B)', '')
        if base_param in default_values:
            return default_values[base_param]

        return None

    def _validate_parameter_format(self, param_name: str, param_value: str) -> bool:
        """验证参数格式"""
        # 使用现有的验证逻辑
        validation_result = self._validate_parameter_value(param_name, param_value)
        return validation_result.get('valid', True)

    def _validate_ab_consistency(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """验证A/B组分一致性"""
        parameters = data.get('parameters', {})

        # 检查A/B组分参数的一致性
        ab_pairs = {}
        for param_name in parameters.keys():
            if param_name.endswith('(A)'):
                base_name = param_name[:-3]
                ab_pairs[base_name] = ab_pairs.get(base_name, {})
                ab_pairs[base_name]['A'] = parameters[param_name]
            elif param_name.endswith('(B)'):
                base_name = param_name[:-3]
                ab_pairs[base_name] = ab_pairs.get(base_name, {})
                ab_pairs[base_name]['B'] = parameters[param_name]

        # 检查每对A/B参数
        for base_name, ab_values in ab_pairs.items():
            if 'A' in ab_values and 'B' in ab_values:
                a_value = ab_values['A']
                b_value = ab_values['B']

                # 检查是否都是错误值
                if self._is_error_value(a_value) and self._is_error_value(b_value):
                    logger.warning(f"A/B组分都是错误值: {base_name}")

                # 检查是否一个是错误值，另一个是正确值
                elif self._is_error_value(a_value) and not self._is_error_value(b_value):
                    logger.info(f"A组分值异常，B组分正常: {base_name}")
                elif not self._is_error_value(a_value) and self._is_error_value(b_value):
                    logger.info(f"B组分值异常，A组分正常: {base_name}")

        return data

    def _validate_data_completeness(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """验证数据完整性"""
        parameters = data.get('parameters', {})

        # 检查关键参数是否存在
        critical_params = ['外观', '粘度', '密度', '混合比例']
        missing_params = []

        for param in critical_params:
            # 检查是否存在该参数（包括A/B组分）
            found = False
            for param_name in parameters.keys():
                if param in param_name:
                    found = True
                    break

            if not found:
                missing_params.append(param)

        if missing_params:
            logger.warning(f"缺少关键参数: {missing_params}")

        return data
    
    def _validate_parameter_value(self, param: str, value: Any) -> Dict[str, Any]:
        """验证单个参数值"""
        value_str = str(value).strip()
        
        # 查找匹配的验证规则
        rule = None
        for rule_key, rule_config in self.validation_rules.items():
            if rule_key in param:
                rule = rule_config
                break
        
        if not rule:
            return {'valid': True}  # 没有规则则认为有效
        
        try:
            if rule['type'] == 'numeric':
                return self._validate_numeric(value_str, rule)
            elif rule['type'] == 'scientific':
                return self._validate_scientific(value_str, rule)
            elif rule['type'] == 'percentage':
                return self._validate_percentage(value_str, rule)
            elif rule['type'] == 'text':
                return self._validate_text(value_str, rule)
            elif rule['type'] == 'ratio':
                return self._validate_ratio(value_str, rule)
            elif rule['type'] == 'grade':
                return self._validate_grade(value_str, rule)
            elif rule['type'] == 'temperature_range':
                return self._validate_temperature_range(value_str, rule)
            else:
                return {'valid': True}
                
        except Exception as e:
            logger.warning(f"验证参数 {param} 时出错: {e}")
            return {'valid': False, 'reason': f'验证异常: {str(e)}'}
    
    def _validate_numeric(self, value: str, rule: Dict) -> Dict[str, Any]:
        """验证数值"""
        # 提取数值
        numbers = re.findall(r'[\d.]+', value)
        if not numbers:
            return {'valid': False, 'reason': '无法提取数值'}
        
        try:
            num = float(numbers[0])
            if num < rule.get('min', float('-inf')):
                return {'valid': False, 'reason': f'数值过小 (< {rule["min"]})'}
            if num > rule.get('max', float('inf')):
                return {'valid': False, 'reason': f'数值过大 (> {rule["max"]})'}
            
            return {'valid': True}
        except ValueError:
            return {'valid': False, 'reason': '数值格式错误'}
    
    def _validate_scientific(self, value: str, rule: Dict) -> Dict[str, Any]:
        """验证科学计数法"""
        # 匹配科学计数法格式
        pattern = r'>?(\d+(?:\.\d+)?)[eE]?(\d+)?'
        match = re.search(pattern, value)
        
        if not match:
            return {'valid': False, 'reason': '科学计数法格式错误'}
        
        try:
            base = float(match.group(1))
            exp = int(match.group(2)) if match.group(2) else 0
            num = base * (10 ** exp)
            
            if num < rule.get('min', 0):
                return {'valid': False, 'reason': '数值过小'}
            if num > rule.get('max', float('inf')):
                return {'valid': False, 'reason': '数值过大'}
            
            return {'valid': True}
        except (ValueError, TypeError):
            return {'valid': False, 'reason': '科学计数法解析错误'}
    
    def _validate_percentage(self, value: str, rule: Dict) -> Dict[str, Any]:
        """验证百分比"""
        numbers = re.findall(r'[\d.]+', value)
        if not numbers:
            return {'valid': False, 'reason': '无法提取百分比数值'}
        
        try:
            num = float(numbers[0])
            if num < 0 or num > 100:
                return {'valid': False, 'reason': '百分比超出范围 (0-100)'}
            return {'valid': True}
        except ValueError:
            return {'valid': False, 'reason': '百分比格式错误'}
    
    def _validate_text(self, value: str, rule: Dict) -> Dict[str, Any]:
        """验证文本"""
        required_words = rule.get('required_words', [])
        if required_words:
            if not any(word in value for word in required_words):
                return {
                    'valid': True,  # 不算错误，只是警告
                    'warning': f'可能包含非标准描述，建议包含: {", ".join(required_words)}'
                }
        return {'valid': True}
    
    def _validate_ratio(self, value: str, rule: Dict) -> Dict[str, Any]:
        """验证比例"""
        pattern = rule.get('pattern', r'\d+:\d+')
        if not re.search(pattern, value):
            return {'valid': False, 'reason': '比例格式错误，应为 数字:数字'}
        return {'valid': True}
    
    def _validate_grade(self, value: str, rule: Dict) -> Dict[str, Any]:
        """验证等级"""
        valid_values = rule.get('valid_values', [])
        if value not in valid_values:
            return {
                'valid': False, 
                'reason': f'等级无效，有效值: {", ".join(valid_values)}'
            }
        return {'valid': True}
    
    def _validate_temperature_range(self, value: str, rule: Dict) -> Dict[str, Any]:
        """验证温度范围"""
        pattern = rule.get('pattern', r'-?\d+~\d+')
        if not re.search(pattern, value):
            return {'valid': False, 'reason': '温度范围格式错误，应为 数字~数字'}
        return {'valid': True}
    
    def _calculate_quality_score(self, tech_params: Dict[str, Any], 
                                missing_params: List[str], 
                                invalid_params: List[Tuple]) -> float:
        """计算质量评分 (0-100)"""
        total_score = 100.0
        
        # 缺失参数扣分
        missing_penalty = len(missing_params) * 5  # 每个缺失参数扣5分
        total_score -= missing_penalty
        
        # 无效参数扣分
        invalid_penalty = len(invalid_params) * 10  # 每个无效参数扣10分
        total_score -= invalid_penalty
        
        # 重要参数缺失额外扣分
        important_missing = [p for p in missing_params if p in self.important_params]
        important_penalty = len(important_missing) * 5  # 重要参数额外扣5分
        total_score -= important_penalty
        
        # 确保分数在0-100范围内
        return max(0.0, min(100.0, total_score))
    
    def generate_validation_summary(self, results: List[ValidationResult]) -> Dict[str, Any]:
        """生成验证摘要"""
        if not results:
            return {}
        
        total_files = len(results)
        valid_files = len([r for r in results if r.is_valid])
        avg_quality = sum(r.quality_score for r in results) / total_files
        avg_completion = sum(r.completion_rate for r in results) / total_files
        
        # 统计最常见的问题
        all_missing = []
        all_invalid = []
        for result in results:
            all_missing.extend(result.missing_params)
            all_invalid.extend([param for param, _, _ in result.invalid_params])
        
        from collections import Counter
        common_missing = Counter(all_missing).most_common(5)
        common_invalid = Counter(all_invalid).most_common(5)
        
        return {
            'total_files': total_files,
            'valid_files': valid_files,
            'invalid_files': total_files - valid_files,
            'validation_rate': (valid_files / total_files) * 100,
            'average_quality_score': avg_quality,
            'average_completion_rate': avg_completion,
            'common_missing_params': common_missing,
            'common_invalid_params': common_invalid,
            'quality_distribution': self._get_quality_distribution(results)
        }
    
    def _get_quality_distribution(self, results: List[ValidationResult]) -> Dict[str, int]:
        """获取质量分布"""
        distribution = {'优秀(90-100)': 0, '良好(80-89)': 0, '一般(70-79)': 0, '较差(<70)': 0}
        
        for result in results:
            score = result.quality_score
            if score >= 90:
                distribution['优秀(90-100)'] += 1
            elif score >= 80:
                distribution['良好(80-89)'] += 1
            elif score >= 70:
                distribution['一般(70-79)'] += 1
            else:
                distribution['较差(<70)'] += 1
        
        return distribution
