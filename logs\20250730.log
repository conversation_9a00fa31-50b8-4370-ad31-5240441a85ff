2025-07-30 08:30:34,787 - src.utils.config_manager - INFO - logger.py:76 - 成功加载配置文件: config.json
2025-07-30 08:30:34,787 - src.utils.config_manager - INFO - logger.py:76 - 加载学习映射: 7 个映射
2025-07-30 08:31:24,718 - src.extraction.base_extractor - INFO - logger.py:76 - Word提取器初始化完成
2025-07-30 08:31:24,719 - src.extraction.base_extractor - INFO - logger.py:76 - 开始提取Word内容: FM-109-17技术参数表20250101.docx
2025-07-30 08:31:24,772 - src.extraction.base_extractor - DEBUG - logger.py:72 - 提取嵌套表格: 5 行数据
2025-07-30 08:31:24,780 - src.extraction.base_extractor - DEBUG - logger.py:72 - 提取嵌套表格: 6 行数据
2025-07-30 08:31:24,785 - src.extraction.base_extractor - DEBUG - logger.py:72 - 提取嵌套表格: 8 行数据
2025-07-30 08:31:24,793 - src.extraction.base_extractor - DEBUG - logger.py:72 - 提取嵌套表格: 3 行数据
2025-07-30 08:31:24,796 - src.extraction.base_extractor - DEBUG - logger.py:72 - 从文件名提取产品名称: FM-109-17
2025-07-30 08:31:24,796 - src.extraction.base_extractor - INFO - logger.py:76 - Word内容提取完成: 2 个表格, 0 个段落
2025-07-30 08:31:24,814 - src.summary_table.config_manager - INFO - logger.py:76 - 成功加载配置文件: config.json
2025-07-30 08:31:24,815 - src.extraction.base_extractor - INFO - logger.py:76 - PDF提取器初始化完成
2025-07-30 08:31:24,815 - src.extraction.base_extractor - INFO - logger.py:76 - Word提取器初始化完成
2025-07-30 08:31:24,815 - src.summary_table.parameter_mapper - INFO - logger.py:76 - 参数映射器初始化完成，支持 34 个参数
2025-07-30 08:31:24,816 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - A/B组分提取器初始化完成
2025-07-30 08:31:24,816 - src.summary_table.data_extractor - INFO - logger.py:76 - 技术数据提取器初始化完成
2025-07-30 08:31:24,816 - src.summary_table.data_extractor - INFO - logger.py:76 - 开始提取文件: FM-109-17技术参数表20250101.docx
2025-07-30 08:31:24,816 - src.extraction.base_extractor - INFO - logger.py:76 - 开始提取Word内容: FM-109-17技术参数表20250101.docx
2025-07-30 08:31:24,828 - src.extraction.base_extractor - DEBUG - logger.py:72 - 提取嵌套表格: 5 行数据
2025-07-30 08:31:24,831 - src.extraction.base_extractor - DEBUG - logger.py:72 - 提取嵌套表格: 6 行数据
2025-07-30 08:31:24,834 - src.extraction.base_extractor - DEBUG - logger.py:72 - 提取嵌套表格: 8 行数据
2025-07-30 08:31:24,836 - src.extraction.base_extractor - DEBUG - logger.py:72 - 提取嵌套表格: 3 行数据
2025-07-30 08:31:24,838 - src.extraction.base_extractor - DEBUG - logger.py:72 - 从文件名提取产品名称: FM-109-17
2025-07-30 08:31:24,838 - src.extraction.base_extractor - INFO - logger.py:76 - Word内容提取完成: 2 个表格, 0 个段落
2025-07-30 08:31:24,840 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - A组分列匹配: '109-17A' -> 模式: ^.*A$
2025-07-30 08:31:24,840 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - B组分列匹配: '109-17B' -> 模式: ^.*B$
2025-07-30 08:31:24,840 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到有效A/B列: A列=2('109-17A'), B列=3('109-17B')
2025-07-30 08:31:24,840 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到核心参数: 外观 在 '外观'
2025-07-30 08:31:24,840 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到核心参数: 粘度 在 '粘度'
2025-07-30 08:31:24,842 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到核心参数: 密度 在 '密度'
2025-07-30 08:31:24,842 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - A/B组分表格验证通过，找到 3 个核心参数，1 个可选参数，外观参数: 是
2025-07-30 08:31:24,842 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到A/B组分表格，页面: 1
2025-07-30 08:31:24,842 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - A组分列匹配: '109-17A' -> 模式: ^.*A$
2025-07-30 08:31:24,842 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - B组分列匹配: '109-17B' -> 模式: ^.*B$
2025-07-30 08:31:24,842 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到有效A/B列: A列=2('109-17A'), B列=3('109-17B')
2025-07-30 08:31:24,842 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 检查A组分数据: '外观' -> 列2 = '黄色透明液体', 是否为单位: False
2025-07-30 08:31:24,842 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - ✓ 提取A组分: 外观(A) = 黄色透明液体
2025-07-30 08:31:24,842 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 检查B组分数据: '外观' -> 列3 = '黄色透明/黑色液体', 是否为单位: False
2025-07-30 08:31:24,843 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - ✓ 提取B组分: 外观(B) = 黄色透明/黑色液体
2025-07-30 08:31:24,844 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 检查A组分数据: '粘度' -> 列2 = '400±200', 是否为单位: False
2025-07-30 08:31:24,844 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - ✓ 提取A组分: 粘度(A) = 400±200
2025-07-30 08:31:24,844 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 检查B组分数据: '粘度' -> 列3 = '700±200', 是否为单位: False
2025-07-30 08:31:24,844 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - ✓ 提取B组分: 粘度(B) = 700±200
2025-07-30 08:31:24,844 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 检查A组分数据: '密度' -> 列2 = '1.10±0.05', 是否为单位: False
2025-07-30 08:31:24,844 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - ✓ 提取A组分: 密度(A) = 1.10±0.05
2025-07-30 08:31:24,844 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 检查B组分数据: '密度' -> 列3 = '0.96±0.05', 是否为单位: False
2025-07-30 08:31:24,845 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - ✓ 提取B组分: 密度(B) = 0.96±0.05
2025-07-30 08:31:24,845 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 检查A组分数据: '保存期限' -> 列2 = '6个月', 是否为单位: False
2025-07-30 08:31:24,845 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - ✓ 提取A组分: 保存期限(A) = 6个月
2025-07-30 08:31:24,846 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 检查B组分数据: '保存期限' -> 列3 = '6个月', 是否为单位: False
2025-07-30 08:31:24,846 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - ✓ 提取B组分: 保存期限(B) = 6个月
2025-07-30 08:31:24,847 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 未匹配参数: '混合比例' -> A: '100:100', B: '100:100')
2025-07-30 08:31:24,847 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 跳过重复A组分: 粘度(A) (已存在: 400±200)
2025-07-30 08:31:24,847 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 跳过重复B组分: 粘度(B) (已存在: 700±200)
2025-07-30 08:31:24,847 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 跳过重复A组分: 密度(A) (已存在: 1.10±0.05)
2025-07-30 08:31:24,847 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 跳过重复B组分: 密度(B) (已存在: 0.96±0.05)
2025-07-30 08:31:24,847 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 未匹配参数: '凝胶时间' -> A: '90~150(100g)', B: '30~40(20g)')
2025-07-30 08:31:24,847 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 未匹配参数: '固化时间*' -> A: '60/3~6 | 23/48~72', B: '60/3~6 | 23/48~72')
2025-07-30 08:31:24,847 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 未匹配参数: '硬度' -> A: '35±10', B: 'GB/T 531.1-2008')
2025-07-30 08:31:24,848 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 未匹配参数: '体积电阻率' -> A: '>1011', B: 'GB/T 1692-2008')
2025-07-30 08:31:24,848 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 未匹配参数: '绝缘强度' -> A: '>15', B: 'GB/T 1408.1-2016')
2025-07-30 08:31:24,848 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 未匹配参数: '吸水率' -> A: '＜0.3', B: '企业标准')
2025-07-30 08:31:24,848 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 未匹配参数: '拉伸强度' -> A: '>0.3', B: 'GB/T 528-2009')
2025-07-30 08:31:24,848 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 未匹配参数: '双85测试' -> A: '＞500', B: '企业标准')
2025-07-30 08:31:24,849 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 未匹配参数: '阻燃等级' -> A: 'V-0', B: 'UL-94')
2025-07-30 08:31:24,849 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 未匹配参数: '应用温度范围' -> A: '-60~100', B: '企业标准')
2025-07-30 08:31:24,849 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - A/B组分提取完成，提取到 8 个参数
2025-07-30 08:31:24,849 - src.summary_table.data_extractor - INFO - logger.py:76 - A/B组分提取器提取到 8 个参数
2025-07-30 08:31:24,850 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - A组分列匹配: '109-17A' -> 模式: ^.*A$
2025-07-30 08:31:24,850 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - B组分列匹配: '109-17B' -> 模式: ^.*B$
2025-07-30 08:31:24,850 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到有效A/B列: A列=2('109-17A'), B列=3('109-17B')
2025-07-30 08:31:24,850 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到核心参数: 外观 在 '外观'
2025-07-30 08:31:24,850 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到核心参数: 粘度 在 '粘度'
2025-07-30 08:31:24,850 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到核心参数: 密度 在 '密度'
2025-07-30 08:31:24,850 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - A/B组分表格验证通过，找到 3 个核心参数，1 个可选参数，外观参数: 是
2025-07-30 08:31:24,850 - src.summary_table.data_extractor - DEBUG - logger.py:72 - 从表头提取产品描述: FM-109-17是一款阻燃型双组份聚氨酯密封胶，符合环保要求，外观透明、硬度适中，防水性能和绝缘性...
2025-07-30 08:31:24,850 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - A组分列匹配: '109-17A' -> 模式: ^.*A$
2025-07-30 08:31:24,850 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - B组分列匹配: '109-17B' -> 模式: ^.*B$
2025-07-30 08:31:24,850 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到有效A/B列: A列=2('109-17A'), B列=3('109-17B')
2025-07-30 08:31:24,850 - src.summary_table.data_extractor - DEBUG - logger.py:72 - 提取非A/B参数: 混合比例 = 100:100
2025-07-30 08:31:24,850 - src.summary_table.data_extractor - DEBUG - logger.py:72 - 提取非A/B参数: 混合粘度 = 400±200
2025-07-30 08:31:24,850 - src.summary_table.data_extractor - DEBUG - logger.py:72 - 提取非A/B参数: 混合密度 = 1.05±0.05
2025-07-30 08:31:24,851 - src.summary_table.data_extractor - DEBUG - logger.py:72 - 提取非A/B参数: 凝胶时间 = 90~150(100g)
2025-07-30 08:31:24,851 - src.summary_table.data_extractor - DEBUG - logger.py:72 - 提取非A/B参数: 固化时间* = 60/3~6 | 23/48~72
2025-07-30 08:31:24,851 - src.summary_table.data_extractor - DEBUG - logger.py:72 - 提取非A/B参数: 硬度 = 35±10
2025-07-30 08:31:24,851 - src.summary_table.data_extractor - DEBUG - logger.py:72 - 提取非A/B参数: 体积电阻率 = >1011
2025-07-30 08:31:24,851 - src.summary_table.data_extractor - DEBUG - logger.py:72 - 提取非A/B参数: 绝缘强度 = >15
2025-07-30 08:31:24,851 - src.summary_table.data_extractor - DEBUG - logger.py:72 - 提取非A/B参数: 吸水率 = ＜0.3
2025-07-30 08:31:24,851 - src.summary_table.data_extractor - DEBUG - logger.py:72 - 提取非A/B参数: 拉伸强度 = >0.3
2025-07-30 08:31:24,851 - src.summary_table.data_extractor - DEBUG - logger.py:72 - 提取非A/B参数: 双85测试 = ＞500
2025-07-30 08:31:24,851 - src.summary_table.data_extractor - DEBUG - logger.py:72 - 提取非A/B参数: 阻燃等级 = V-0
2025-07-30 08:31:24,851 - src.summary_table.data_extractor - DEBUG - logger.py:72 - 提取非A/B参数: 应用温度范围 = -60~100
2025-07-30 08:31:24,851 - src.summary_table.data_extractor - DEBUG - logger.py:72 - 从A/B组分表格提取非A/B参数: 14 个
2025-07-30 08:31:24,854 - src.summary_table.data_extractor - DEBUG - logger.py:72 - 从普通表格提取参数: 6 个
2025-07-30 08:31:24,854 - src.summary_table.data_extractor - DEBUG - logger.py:72 - 总共提取到 28 个原始参数
2025-07-30 08:31:24,854 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 保留A/B组分参数: 外观(A) = 黄色透明液体
2025-07-30 08:31:24,854 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 保留A/B组分参数: 外观(B) = 黄色透明/黑色液体
2025-07-30 08:31:24,855 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 保留A/B组分参数: 粘度(A) = 400±200
2025-07-30 08:31:24,855 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 保留A/B组分参数: 粘度(B) = 700±200
2025-07-30 08:31:24,855 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 保留A/B组分参数: 密度(A) = 1.10±0.05
2025-07-30 08:31:24,855 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 保留A/B组分参数: 密度(B) = 0.96±0.05
2025-07-30 08:31:24,855 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 保留A/B组分参数: 保存期限(A) = 6个月
2025-07-30 08:31:24,855 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 保留A/B组分参数: 保存期限(B) = 6个月
2025-07-30 08:31:24,857 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 产品描述 -> 性能特点 (置信度: 1.00)
2025-07-30 08:31:24,857 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 混合比例 -> 混合比例 (置信度: 1.00)
2025-07-30 08:31:24,857 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 混合粘度 -> 混合粘度 (置信度: 1.00)
2025-07-30 08:31:24,857 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 混合密度 -> 固化后密度 (置信度: 1.00)
2025-07-30 08:31:24,857 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 凝胶时间 -> 不流动时间 (置信度: 1.00)
2025-07-30 08:31:24,857 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 固化时间* -> 固化时间 (置信度: 1.00)
2025-07-30 08:31:24,857 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 硬度 -> 硬度 (置信度: 1.00)
2025-07-30 08:31:24,857 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 体积电阻率 -> 体积电阻率 (置信度: 1.00)
2025-07-30 08:31:24,857 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 绝缘强度 -> 绝缘强度 (置信度: 1.00)
2025-07-30 08:31:24,857 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 吸水率 -> 吸水率 (置信度: 1.00)
2025-07-30 08:31:24,857 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 拉伸强度 -> 拉伸强度 (置信度: 1.00)
2025-07-30 08:31:24,857 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 双85测试 -> 双85测试 (置信度: 0.75)
2025-07-30 08:31:24,857 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 阻燃等级 -> 阻燃 (置信度: 1.00)
2025-07-30 08:31:24,857 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 应用温度范围 -> 应用温度 (置信度: 1.00)
2025-07-30 08:31:24,857 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 未找到匹配的参数: 推荐工艺
2025-07-30 08:31:24,857 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 未找到匹配的参数: 包装规格
2025-07-30 08:31:24,858 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 未找到匹配的参数: 规格1
2025-07-30 08:31:24,858 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 未找到匹配的参数: 规格2
2025-07-30 08:31:24,858 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 未找到匹配的参数: 储存运输
2025-07-30 08:31:24,858 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 未找到匹配的参数: 重要说明
2025-07-30 08:31:24,858 - src.summary_table.parameter_mapper - INFO - logger.py:76 - 参数映射完成，映射了 22 个参数
2025-07-30 08:31:24,858 - src.summary_table.data_extractor - INFO - logger.py:76 - Word提取完成: 22 个参数
2025-07-30 08:31:24,861 - src.summary_table.config_manager - INFO - logger.py:76 - 成功加载配置文件: config.json
2025-07-30 08:31:24,862 - src.extraction.base_extractor - INFO - logger.py:76 - PDF提取器初始化完成
2025-07-30 08:31:24,862 - src.extraction.base_extractor - INFO - logger.py:76 - Word提取器初始化完成
2025-07-30 08:31:24,862 - src.summary_table.parameter_mapper - INFO - logger.py:76 - 参数映射器初始化完成，支持 34 个参数
2025-07-30 08:31:24,862 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - A/B组分提取器初始化完成
2025-07-30 08:31:24,863 - src.summary_table.data_extractor - INFO - logger.py:76 - 技术数据提取器初始化完成
2025-07-30 08:31:24,863 - src.extraction.base_extractor - INFO - logger.py:76 - Word提取器初始化完成
2025-07-30 08:31:24,863 - src.extraction.base_extractor - INFO - logger.py:76 - 开始提取Word内容: FM-109-17技术参数表20250101.docx
2025-07-30 08:31:24,883 - src.extraction.base_extractor - DEBUG - logger.py:72 - 提取嵌套表格: 5 行数据
2025-07-30 08:31:24,887 - src.extraction.base_extractor - DEBUG - logger.py:72 - 提取嵌套表格: 6 行数据
2025-07-30 08:31:24,890 - src.extraction.base_extractor - DEBUG - logger.py:72 - 提取嵌套表格: 8 行数据
2025-07-30 08:31:24,895 - src.extraction.base_extractor - DEBUG - logger.py:72 - 提取嵌套表格: 3 行数据
2025-07-30 08:31:24,896 - src.extraction.base_extractor - DEBUG - logger.py:72 - 从文件名提取产品名称: FM-109-17
2025-07-30 08:31:24,896 - src.extraction.base_extractor - INFO - logger.py:76 - Word内容提取完成: 2 个表格, 0 个段落
2025-07-30 08:31:24,897 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - A组分列匹配: '109-17A' -> 模式: ^.*A$
2025-07-30 08:31:24,897 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - B组分列匹配: '109-17B' -> 模式: ^.*B$
2025-07-30 08:31:24,897 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到有效A/B列: A列=2('109-17A'), B列=3('109-17B')
2025-07-30 08:31:24,897 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到核心参数: 外观 在 '外观'
2025-07-30 08:31:24,897 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到核心参数: 粘度 在 '粘度'
2025-07-30 08:31:24,897 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到核心参数: 密度 在 '密度'
2025-07-30 08:31:24,897 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - A/B组分表格验证通过，找到 3 个核心参数，1 个可选参数，外观参数: 是
2025-07-30 08:31:24,897 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到A/B组分表格，页面: 1
2025-07-30 08:31:24,897 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - A组分列匹配: '109-17A' -> 模式: ^.*A$
2025-07-30 08:31:24,897 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - B组分列匹配: '109-17B' -> 模式: ^.*B$
2025-07-30 08:31:24,899 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到有效A/B列: A列=2('109-17A'), B列=3('109-17B')
2025-07-30 08:31:24,899 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 检查A组分数据: '外观' -> 列2 = '黄色透明液体', 是否为单位: False
2025-07-30 08:31:24,899 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - ✓ 提取A组分: 外观(A) = 黄色透明液体
2025-07-30 08:31:24,900 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 检查B组分数据: '外观' -> 列3 = '黄色透明/黑色液体', 是否为单位: False
2025-07-30 08:31:24,900 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - ✓ 提取B组分: 外观(B) = 黄色透明/黑色液体
2025-07-30 08:31:24,900 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 检查A组分数据: '粘度' -> 列2 = '400±200', 是否为单位: False
2025-07-30 08:31:24,900 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - ✓ 提取A组分: 粘度(A) = 400±200
2025-07-30 08:31:24,901 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 检查B组分数据: '粘度' -> 列3 = '700±200', 是否为单位: False
2025-07-30 08:31:24,901 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - ✓ 提取B组分: 粘度(B) = 700±200
2025-07-30 08:31:24,902 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 检查A组分数据: '密度' -> 列2 = '1.10±0.05', 是否为单位: False
2025-07-30 08:31:24,902 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - ✓ 提取A组分: 密度(A) = 1.10±0.05
2025-07-30 08:31:24,902 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 检查B组分数据: '密度' -> 列3 = '0.96±0.05', 是否为单位: False
2025-07-30 08:31:24,902 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - ✓ 提取B组分: 密度(B) = 0.96±0.05
2025-07-30 08:31:24,903 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 检查A组分数据: '保存期限' -> 列2 = '6个月', 是否为单位: False
2025-07-30 08:31:24,903 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - ✓ 提取A组分: 保存期限(A) = 6个月
2025-07-30 08:31:24,904 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 检查B组分数据: '保存期限' -> 列3 = '6个月', 是否为单位: False
2025-07-30 08:31:24,904 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - ✓ 提取B组分: 保存期限(B) = 6个月
2025-07-30 08:31:24,904 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 未匹配参数: '混合比例' -> A: '100:100', B: '100:100')
2025-07-30 08:31:24,905 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 跳过重复A组分: 粘度(A) (已存在: 400±200)
2025-07-30 08:31:24,905 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 跳过重复B组分: 粘度(B) (已存在: 700±200)
2025-07-30 08:31:24,905 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 跳过重复A组分: 密度(A) (已存在: 1.10±0.05)
2025-07-30 08:31:24,905 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 跳过重复B组分: 密度(B) (已存在: 0.96±0.05)
2025-07-30 08:31:24,905 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 未匹配参数: '凝胶时间' -> A: '90~150(100g)', B: '30~40(20g)')
2025-07-30 08:31:24,905 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 未匹配参数: '固化时间*' -> A: '60/3~6 | 23/48~72', B: '60/3~6 | 23/48~72')
2025-07-30 08:31:24,905 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 未匹配参数: '硬度' -> A: '35±10', B: 'GB/T 531.1-2008')
2025-07-30 08:31:24,905 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 未匹配参数: '体积电阻率' -> A: '>1011', B: 'GB/T 1692-2008')
2025-07-30 08:31:24,905 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 未匹配参数: '绝缘强度' -> A: '>15', B: 'GB/T 1408.1-2016')
2025-07-30 08:31:24,905 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 未匹配参数: '吸水率' -> A: '＜0.3', B: '企业标准')
2025-07-30 08:31:24,905 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 未匹配参数: '拉伸强度' -> A: '>0.3', B: 'GB/T 528-2009')
2025-07-30 08:31:24,905 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 未匹配参数: '双85测试' -> A: '＞500', B: '企业标准')
2025-07-30 08:31:24,905 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 未匹配参数: '阻燃等级' -> A: 'V-0', B: 'UL-94')
2025-07-30 08:31:24,905 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 未匹配参数: '应用温度范围' -> A: '-60~100', B: '企业标准')
2025-07-30 08:31:24,905 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - A/B组分提取完成，提取到 8 个参数
2025-07-30 08:31:24,906 - src.summary_table.data_extractor - INFO - logger.py:76 - A/B组分提取器提取到 8 个参数
2025-07-30 08:31:24,906 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - A组分列匹配: '109-17A' -> 模式: ^.*A$
2025-07-30 08:31:24,906 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - B组分列匹配: '109-17B' -> 模式: ^.*B$
2025-07-30 08:31:24,907 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到有效A/B列: A列=2('109-17A'), B列=3('109-17B')
2025-07-30 08:31:24,907 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到核心参数: 外观 在 '外观'
2025-07-30 08:31:24,907 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到核心参数: 粘度 在 '粘度'
2025-07-30 08:31:24,907 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到核心参数: 密度 在 '密度'
2025-07-30 08:31:24,907 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - A/B组分表格验证通过，找到 3 个核心参数，1 个可选参数，外观参数: 是
2025-07-30 08:31:24,907 - src.summary_table.data_extractor - DEBUG - logger.py:72 - 从表头提取产品描述: FM-109-17是一款阻燃型双组份聚氨酯密封胶，符合环保要求，外观透明、硬度适中，防水性能和绝缘性...
2025-07-30 08:31:24,907 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - A组分列匹配: '109-17A' -> 模式: ^.*A$
2025-07-30 08:31:24,907 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - B组分列匹配: '109-17B' -> 模式: ^.*B$
2025-07-30 08:31:24,907 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到有效A/B列: A列=2('109-17A'), B列=3('109-17B')
2025-07-30 08:31:24,907 - src.summary_table.data_extractor - DEBUG - logger.py:72 - 提取非A/B参数: 混合比例 = 100:100
2025-07-30 08:31:24,907 - src.summary_table.data_extractor - DEBUG - logger.py:72 - 提取非A/B参数: 混合粘度 = 400±200
2025-07-30 08:31:24,908 - src.summary_table.data_extractor - DEBUG - logger.py:72 - 提取非A/B参数: 混合密度 = 1.05±0.05
2025-07-30 08:31:24,908 - src.summary_table.data_extractor - DEBUG - logger.py:72 - 提取非A/B参数: 凝胶时间 = 90~150(100g)
2025-07-30 08:31:24,908 - src.summary_table.data_extractor - DEBUG - logger.py:72 - 提取非A/B参数: 固化时间* = 60/3~6 | 23/48~72
2025-07-30 08:31:24,908 - src.summary_table.data_extractor - DEBUG - logger.py:72 - 提取非A/B参数: 硬度 = 35±10
2025-07-30 08:31:24,908 - src.summary_table.data_extractor - DEBUG - logger.py:72 - 提取非A/B参数: 体积电阻率 = >1011
2025-07-30 08:31:24,908 - src.summary_table.data_extractor - DEBUG - logger.py:72 - 提取非A/B参数: 绝缘强度 = >15
2025-07-30 08:31:24,908 - src.summary_table.data_extractor - DEBUG - logger.py:72 - 提取非A/B参数: 吸水率 = ＜0.3
2025-07-30 08:31:24,908 - src.summary_table.data_extractor - DEBUG - logger.py:72 - 提取非A/B参数: 拉伸强度 = >0.3
2025-07-30 08:31:24,908 - src.summary_table.data_extractor - DEBUG - logger.py:72 - 提取非A/B参数: 双85测试 = ＞500
2025-07-30 08:31:24,908 - src.summary_table.data_extractor - DEBUG - logger.py:72 - 提取非A/B参数: 阻燃等级 = V-0
2025-07-30 08:31:24,908 - src.summary_table.data_extractor - DEBUG - logger.py:72 - 提取非A/B参数: 应用温度范围 = -60~100
2025-07-30 08:31:24,908 - src.summary_table.data_extractor - DEBUG - logger.py:72 - 从A/B组分表格提取非A/B参数: 14 个
2025-07-30 08:31:24,908 - src.summary_table.data_extractor - DEBUG - logger.py:72 - 从普通表格提取参数: 6 个
2025-07-30 08:31:24,908 - src.summary_table.data_extractor - DEBUG - logger.py:72 - 总共提取到 28 个原始参数
2025-07-30 08:31:24,910 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - A/B组分提取器初始化完成
2025-07-30 08:31:24,910 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - A组分列匹配: '109-17A' -> 模式: ^.*A$
2025-07-30 08:31:24,911 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - B组分列匹配: '109-17B' -> 模式: ^.*B$
2025-07-30 08:31:24,911 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到有效A/B列: A列=2('109-17A'), B列=3('109-17B')
2025-07-30 08:31:24,911 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到核心参数: 外观 在 '外观'
2025-07-30 08:31:24,911 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到核心参数: 粘度 在 '粘度'
2025-07-30 08:31:24,911 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到核心参数: 密度 在 '密度'
2025-07-30 08:31:24,911 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - A/B组分表格验证通过，找到 3 个核心参数，1 个可选参数，外观参数: 是
2025-07-30 08:31:24,911 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到A/B组分表格，页面: 1
2025-07-30 08:31:24,911 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - A组分列匹配: '109-17A' -> 模式: ^.*A$
2025-07-30 08:31:24,911 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - B组分列匹配: '109-17B' -> 模式: ^.*B$
2025-07-30 08:31:24,911 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到有效A/B列: A列=2('109-17A'), B列=3('109-17B')
2025-07-30 08:31:24,911 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到核心参数: 外观 在 '外观'
2025-07-30 08:31:24,911 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到核心参数: 粘度 在 '粘度'
2025-07-30 08:31:24,911 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到核心参数: 密度 在 '密度'
2025-07-30 08:31:24,911 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - A/B组分表格验证通过，找到 3 个核心参数，1 个可选参数，外观参数: 是
2025-07-30 08:31:24,911 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到A/B组分表格，页面: 1
2025-07-30 08:31:24,911 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - A组分列匹配: '109-17A' -> 模式: ^.*A$
2025-07-30 08:31:24,911 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - B组分列匹配: '109-17B' -> 模式: ^.*B$
2025-07-30 08:31:24,912 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到有效A/B列: A列=2('109-17A'), B列=3('109-17B')
2025-07-30 08:31:24,912 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 检查A组分数据: '外观' -> 列2 = '黄色透明液体', 是否为单位: False
2025-07-30 08:31:24,912 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - ✓ 提取A组分: 外观(A) = 黄色透明液体
2025-07-30 08:31:24,912 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 检查B组分数据: '外观' -> 列3 = '黄色透明/黑色液体', 是否为单位: False
2025-07-30 08:31:24,912 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - ✓ 提取B组分: 外观(B) = 黄色透明/黑色液体
2025-07-30 08:31:24,912 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 检查A组分数据: '粘度' -> 列2 = '400±200', 是否为单位: False
2025-07-30 08:31:24,912 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - ✓ 提取A组分: 粘度(A) = 400±200
2025-07-30 08:31:24,912 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 检查B组分数据: '粘度' -> 列3 = '700±200', 是否为单位: False
2025-07-30 08:31:24,912 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - ✓ 提取B组分: 粘度(B) = 700±200
2025-07-30 08:31:24,912 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 检查A组分数据: '密度' -> 列2 = '1.10±0.05', 是否为单位: False
2025-07-30 08:31:24,912 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - ✓ 提取A组分: 密度(A) = 1.10±0.05
2025-07-30 08:31:24,913 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 检查B组分数据: '密度' -> 列3 = '0.96±0.05', 是否为单位: False
2025-07-30 08:31:24,913 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - ✓ 提取B组分: 密度(B) = 0.96±0.05
2025-07-30 08:31:24,913 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 检查A组分数据: '保存期限' -> 列2 = '6个月', 是否为单位: False
2025-07-30 08:31:24,913 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - ✓ 提取A组分: 保存期限(A) = 6个月
2025-07-30 08:31:24,913 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 检查B组分数据: '保存期限' -> 列3 = '6个月', 是否为单位: False
2025-07-30 08:31:24,913 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - ✓ 提取B组分: 保存期限(B) = 6个月
2025-07-30 08:31:24,913 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 未匹配参数: '混合比例' -> A: '100:100', B: '100:100')
2025-07-30 08:31:24,913 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 跳过重复A组分: 粘度(A) (已存在: 400±200)
2025-07-30 08:31:24,913 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 跳过重复B组分: 粘度(B) (已存在: 700±200)
2025-07-30 08:31:24,913 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 跳过重复A组分: 密度(A) (已存在: 1.10±0.05)
2025-07-30 08:31:24,913 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 跳过重复B组分: 密度(B) (已存在: 0.96±0.05)
2025-07-30 08:31:24,913 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 未匹配参数: '凝胶时间' -> A: '90~150(100g)', B: '30~40(20g)')
2025-07-30 08:31:24,913 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 未匹配参数: '固化时间*' -> A: '60/3~6 | 23/48~72', B: '60/3~6 | 23/48~72')
2025-07-30 08:31:24,913 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 未匹配参数: '硬度' -> A: '35±10', B: 'GB/T 531.1-2008')
2025-07-30 08:31:24,913 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 未匹配参数: '体积电阻率' -> A: '>1011', B: 'GB/T 1692-2008')
2025-07-30 08:31:24,913 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 未匹配参数: '绝缘强度' -> A: '>15', B: 'GB/T 1408.1-2016')
2025-07-30 08:31:24,913 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 未匹配参数: '吸水率' -> A: '＜0.3', B: '企业标准')
2025-07-30 08:31:24,913 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 未匹配参数: '拉伸强度' -> A: '>0.3', B: 'GB/T 528-2009')
2025-07-30 08:31:24,913 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 未匹配参数: '双85测试' -> A: '＞500', B: '企业标准')
2025-07-30 08:31:24,913 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 未匹配参数: '阻燃等级' -> A: 'V-0', B: 'UL-94')
2025-07-30 08:31:24,913 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 未匹配参数: '应用温度范围' -> A: '-60~100', B: '企业标准')
2025-07-30 08:31:24,913 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - A/B组分提取完成，提取到 8 个参数
2025-07-30 08:31:24,914 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - A组分列匹配: '109-17A' -> 模式: ^.*A$
2025-07-30 08:31:24,914 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - B组分列匹配: '109-17B' -> 模式: ^.*B$
2025-07-30 08:31:24,914 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到有效A/B列: A列=2('109-17A'), B列=3('109-17B')
2025-07-30 08:31:24,914 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到核心参数: 外观 在 '外观'
2025-07-30 08:31:24,914 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到核心参数: 粘度 在 '粘度'
2025-07-30 08:31:24,914 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到核心参数: 密度 在 '密度'
2025-07-30 08:31:24,914 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - A/B组分表格验证通过，找到 3 个核心参数，1 个可选参数，外观参数: 是
2025-07-30 08:31:24,914 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到A/B组分表格，页面: 1
2025-07-30 08:31:24,914 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - A组分列匹配: '109-17A' -> 模式: ^.*A$
2025-07-30 08:31:24,915 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - B组分列匹配: '109-17B' -> 模式: ^.*B$
2025-07-30 08:31:24,915 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到有效A/B列: A列=2('109-17A'), B列=3('109-17B')
2025-07-30 08:31:24,915 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 保留A/B组分参数: 外观(A) = 黄色透明液体
2025-07-30 08:31:24,915 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 保留A/B组分参数: 外观(B) = 黄色透明/黑色液体
2025-07-30 08:31:24,915 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 保留A/B组分参数: 粘度(A) = 400±200
2025-07-30 08:31:24,915 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 保留A/B组分参数: 粘度(B) = 700±200
2025-07-30 08:31:24,915 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 保留A/B组分参数: 密度(A) = 1.10±0.05
2025-07-30 08:31:24,915 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 保留A/B组分参数: 密度(B) = 0.96±0.05
2025-07-30 08:31:24,915 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 保留A/B组分参数: 保存期限(A) = 6个月
2025-07-30 08:31:24,915 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 保留A/B组分参数: 保存期限(B) = 6个月
2025-07-30 08:31:24,915 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 产品描述 -> 性能特点 (置信度: 1.00)
2025-07-30 08:31:24,915 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 混合比例 -> 混合比例 (置信度: 1.00)
2025-07-30 08:31:24,915 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 混合粘度 -> 混合粘度 (置信度: 1.00)
2025-07-30 08:31:24,915 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 混合密度 -> 固化后密度 (置信度: 1.00)
2025-07-30 08:31:24,915 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 凝胶时间 -> 不流动时间 (置信度: 1.00)
2025-07-30 08:31:24,915 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 固化时间* -> 固化时间 (置信度: 1.00)
2025-07-30 08:31:24,915 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 硬度 -> 硬度 (置信度: 1.00)
2025-07-30 08:31:24,915 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 体积电阻率 -> 体积电阻率 (置信度: 1.00)
2025-07-30 08:31:24,915 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 绝缘强度 -> 绝缘强度 (置信度: 1.00)
2025-07-30 08:31:24,916 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 吸水率 -> 吸水率 (置信度: 1.00)
2025-07-30 08:31:24,916 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 拉伸强度 -> 拉伸强度 (置信度: 1.00)
2025-07-30 08:31:24,916 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 双85测试 -> 双85测试 (置信度: 0.75)
2025-07-30 08:31:24,916 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 阻燃等级 -> 阻燃 (置信度: 1.00)
2025-07-30 08:31:24,916 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 应用温度范围 -> 应用温度 (置信度: 1.00)
2025-07-30 08:31:24,916 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 未找到匹配的参数: 推荐工艺
2025-07-30 08:31:24,916 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 未找到匹配的参数: 包装规格
2025-07-30 08:31:24,916 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 未找到匹配的参数: 规格1
2025-07-30 08:31:24,916 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 未找到匹配的参数: 规格2
2025-07-30 08:31:24,916 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 未找到匹配的参数: 储存运输
2025-07-30 08:31:24,916 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 未找到匹配的参数: 重要说明
2025-07-30 08:31:24,916 - src.summary_table.parameter_mapper - INFO - logger.py:76 - 参数映射完成，映射了 22 个参数
2025-07-30 08:31:43,579 - src.utils.config_manager - INFO - logger.py:76 - 成功加载配置文件: config.json
2025-07-30 08:31:43,580 - src.utils.config_manager - INFO - logger.py:76 - 加载学习映射: 7 个映射
2025-07-30 08:31:45,910 - src.extraction.base_extractor - INFO - logger.py:76 - PDF提取器初始化完成
2025-07-30 08:31:45,911 - src.extraction.base_extractor - INFO - logger.py:76 - 开始提取PDF内容: FM-109-17技术参数表.pdf
2025-07-30 08:31:45,931 - src.extraction.base_extractor - DEBUG - logger.py:72 - 处理第 1 页
2025-07-30 08:31:46,073 - src.extraction.base_extractor - DEBUG - logger.py:72 - 第 1 页提取到表格 1: 4 行
2025-07-30 08:31:46,073 - src.extraction.base_extractor - DEBUG - logger.py:72 - 第 1 页提取到表格 2: 5 行
2025-07-30 08:31:46,073 - src.extraction.base_extractor - DEBUG - logger.py:72 - 第 1 页提取到表格 3: 8 行
2025-07-30 08:31:46,075 - src.extraction.base_extractor - DEBUG - logger.py:72 - 第 1 页段落提取失败: name 're' is not defined
2025-07-30 08:31:46,075 - src.extraction.base_extractor - DEBUG - logger.py:72 - 处理第 2 页
2025-07-30 08:31:46,183 - src.extraction.base_extractor - DEBUG - logger.py:72 - 第 2 页提取到表格 1: 4 行
2025-07-30 08:31:46,185 - src.extraction.base_extractor - DEBUG - logger.py:72 - 第 2 页段落提取失败: name 're' is not defined
2025-07-30 08:31:46,185 - src.extraction.base_extractor - DEBUG - logger.py:72 - 从文件名提取产品名称: FM-109-17
2025-07-30 08:31:46,185 - src.extraction.base_extractor - INFO - logger.py:76 - PDF内容提取完成: 4 个表格, 0 个段落
2025-07-30 08:31:46,189 - src.summary_table.config_manager - INFO - logger.py:76 - 成功加载配置文件: config.json
2025-07-30 08:31:46,189 - src.extraction.base_extractor - INFO - logger.py:76 - PDF提取器初始化完成
2025-07-30 08:31:46,190 - src.extraction.base_extractor - INFO - logger.py:76 - Word提取器初始化完成
2025-07-30 08:31:46,190 - src.summary_table.parameter_mapper - INFO - logger.py:76 - 参数映射器初始化完成，支持 34 个参数
2025-07-30 08:31:46,190 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - A/B组分提取器初始化完成
2025-07-30 08:31:46,190 - src.summary_table.data_extractor - INFO - logger.py:76 - 技术数据提取器初始化完成
2025-07-30 08:31:46,190 - src.summary_table.data_extractor - INFO - logger.py:76 - 开始提取文件: FM-109-17技术参数表.pdf
2025-07-30 08:31:46,191 - src.extraction.base_extractor - INFO - logger.py:76 - 开始提取PDF内容: FM-109-17技术参数表.pdf
2025-07-30 08:31:46,194 - src.extraction.base_extractor - DEBUG - logger.py:72 - 处理第 1 页
2025-07-30 08:31:46,337 - src.extraction.base_extractor - DEBUG - logger.py:72 - 第 1 页提取到表格 1: 4 行
2025-07-30 08:31:46,337 - src.extraction.base_extractor - DEBUG - logger.py:72 - 第 1 页提取到表格 2: 5 行
2025-07-30 08:31:46,337 - src.extraction.base_extractor - DEBUG - logger.py:72 - 第 1 页提取到表格 3: 8 行
2025-07-30 08:31:46,339 - src.extraction.base_extractor - DEBUG - logger.py:72 - 第 1 页段落提取失败: name 're' is not defined
2025-07-30 08:31:46,339 - src.extraction.base_extractor - DEBUG - logger.py:72 - 处理第 2 页
2025-07-30 08:31:46,425 - src.extraction.base_extractor - DEBUG - logger.py:72 - 第 2 页提取到表格 1: 4 行
2025-07-30 08:31:46,428 - src.extraction.base_extractor - DEBUG - logger.py:72 - 第 2 页段落提取失败: name 're' is not defined
2025-07-30 08:31:46,428 - src.extraction.base_extractor - DEBUG - logger.py:72 - 从文件名提取产品名称: FM-109-17
2025-07-30 08:31:46,428 - src.extraction.base_extractor - INFO - logger.py:76 - PDF内容提取完成: 4 个表格, 0 个段落
2025-07-30 08:31:46,430 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 未找到A/B组分表格
2025-07-30 08:31:46,432 - src.summary_table.data_extractor - DEBUG - logger.py:72 - 从普通表格提取参数: 4 个
2025-07-30 08:31:46,432 - src.summary_table.data_extractor - DEBUG - logger.py:72 - 从普通表格提取参数: 5 个
2025-07-30 08:31:46,432 - src.summary_table.data_extractor - DEBUG - logger.py:72 - 从普通表格提取参数: 6 个
2025-07-30 08:31:46,432 - src.summary_table.data_extractor - DEBUG - logger.py:72 - 从普通表格提取参数: 4 个
2025-07-30 08:31:46,432 - src.summary_table.data_extractor - DEBUG - logger.py:72 - 总共提取到 19 个原始参数
2025-07-30 08:31:46,435 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 外观 -> 外观(A) (置信度: 1.00)
2025-07-30 08:31:46,435 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 粘度 -> 粘度(A) (置信度: 1.00)
2025-07-30 08:31:46,435 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 密度 -> 密度(A) (置信度: 1.00)
2025-07-30 08:31:46,435 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 保存期限 -> 保存期限(A) (置信度: 1.00)
2025-07-30 08:31:46,435 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 混合比例 -> 混合比例 (置信度: 1.00)
2025-07-30 08:31:46,435 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 混合粘度 -> 混合粘度 (置信度: 1.00)
2025-07-30 08:31:46,435 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 混合密度 -> 固化后密度 (置信度: 1.00)
2025-07-30 08:31:46,435 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 凝胶时间 -> 不流动时间 (置信度: 1.00)
2025-07-30 08:31:46,435 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 固化时间* -> 固化时间 (置信度: 1.00)
2025-07-30 08:31:46,435 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 硬度 -> 硬度 (置信度: 1.00)
2025-07-30 08:31:46,435 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 体积电阻率 -> 体积电阻率 (置信度: 1.00)
2025-07-30 08:31:46,435 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 绝缘强度 -> 绝缘强度 (置信度: 1.00)
2025-07-30 08:31:46,437 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 吸水率 -> 吸水率 (置信度: 1.00)
2025-07-30 08:31:46,437 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 拉伸强度 -> 拉伸强度 (置信度: 1.00)
2025-07-30 08:31:46,437 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 双85测试 -> 双85测试 (置信度: 0.75)
2025-07-30 08:31:46,437 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 未找到匹配的参数: 包装规格
2025-07-30 08:31:46,437 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 未找到匹配的参数: 规格1
2025-07-30 08:31:46,437 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 未找到匹配的参数: 规格2
2025-07-30 08:31:46,437 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 未找到匹配的参数: 规格3
2025-07-30 08:31:46,437 - src.summary_table.parameter_mapper - INFO - logger.py:76 - 参数映射完成，映射了 19 个参数
2025-07-30 08:31:46,438 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 无效的参数名: 外观(A)(A)
2025-07-30 08:31:46,438 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 无效的参数名: 外观(A)(B)
2025-07-30 08:31:46,438 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 无效的参数名: 粘度(A)(A)
2025-07-30 08:31:46,438 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 无效的参数名: 粘度(A)(B)
2025-07-30 08:31:46,438 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 无效的参数名: 密度(A)(A)
2025-07-30 08:31:46,438 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 无效的参数名: 密度(A)(B)
2025-07-30 08:31:46,438 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 无效的参数名: 保存期限(A)(A)
2025-07-30 08:31:46,438 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 无效的参数名: 保存期限(A)(B)
2025-07-30 08:31:46,438 - src.summary_table.data_extractor - INFO - logger.py:76 - PDF提取完成: 11 个参数
2025-07-30 08:31:46,441 - src.summary_table.config_manager - INFO - logger.py:76 - 成功加载配置文件: config.json
2025-07-30 08:31:46,441 - src.extraction.base_extractor - INFO - logger.py:76 - PDF提取器初始化完成
2025-07-30 08:31:46,441 - src.extraction.base_extractor - INFO - logger.py:76 - Word提取器初始化完成
2025-07-30 08:31:46,442 - src.summary_table.parameter_mapper - INFO - logger.py:76 - 参数映射器初始化完成，支持 34 个参数
2025-07-30 08:31:46,442 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - A/B组分提取器初始化完成
2025-07-30 08:31:46,442 - src.summary_table.data_extractor - INFO - logger.py:76 - 技术数据提取器初始化完成
2025-07-30 08:31:46,442 - src.extraction.base_extractor - INFO - logger.py:76 - PDF提取器初始化完成
2025-07-30 08:31:46,442 - src.extraction.base_extractor - INFO - logger.py:76 - 开始提取PDF内容: FM-109-17技术参数表.pdf
2025-07-30 08:31:46,445 - src.extraction.base_extractor - DEBUG - logger.py:72 - 处理第 1 页
2025-07-30 08:31:46,593 - src.extraction.base_extractor - DEBUG - logger.py:72 - 第 1 页提取到表格 1: 4 行
2025-07-30 08:31:46,593 - src.extraction.base_extractor - DEBUG - logger.py:72 - 第 1 页提取到表格 2: 5 行
2025-07-30 08:31:46,594 - src.extraction.base_extractor - DEBUG - logger.py:72 - 第 1 页提取到表格 3: 8 行
2025-07-30 08:31:46,595 - src.extraction.base_extractor - DEBUG - logger.py:72 - 第 1 页段落提取失败: name 're' is not defined
2025-07-30 08:31:46,595 - src.extraction.base_extractor - DEBUG - logger.py:72 - 处理第 2 页
2025-07-30 08:31:46,688 - src.extraction.base_extractor - DEBUG - logger.py:72 - 第 2 页提取到表格 1: 4 行
2025-07-30 08:31:46,690 - src.extraction.base_extractor - DEBUG - logger.py:72 - 第 2 页段落提取失败: name 're' is not defined
2025-07-30 08:31:46,691 - src.extraction.base_extractor - DEBUG - logger.py:72 - 从文件名提取产品名称: FM-109-17
2025-07-30 08:31:46,691 - src.extraction.base_extractor - INFO - logger.py:76 - PDF内容提取完成: 4 个表格, 0 个段落
2025-07-30 08:31:46,692 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 未找到A/B组分表格
2025-07-30 08:31:46,692 - src.summary_table.data_extractor - DEBUG - logger.py:72 - 从普通表格提取参数: 4 个
2025-07-30 08:31:46,692 - src.summary_table.data_extractor - DEBUG - logger.py:72 - 从普通表格提取参数: 5 个
2025-07-30 08:31:46,692 - src.summary_table.data_extractor - DEBUG - logger.py:72 - 从普通表格提取参数: 6 个
2025-07-30 08:31:46,692 - src.summary_table.data_extractor - DEBUG - logger.py:72 - 从普通表格提取参数: 4 个
2025-07-30 08:31:46,692 - src.summary_table.data_extractor - DEBUG - logger.py:72 - 总共提取到 19 个原始参数
2025-07-30 08:31:46,694 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - A/B组分提取器初始化完成
2025-07-30 08:31:46,695 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 未找到A/B组分表格
2025-07-30 08:31:46,696 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 外观 -> 外观(A) (置信度: 1.00)
2025-07-30 08:31:46,696 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 粘度 -> 粘度(A) (置信度: 1.00)
2025-07-30 08:31:46,696 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 密度 -> 密度(A) (置信度: 1.00)
2025-07-30 08:31:46,696 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 保存期限 -> 保存期限(A) (置信度: 1.00)
2025-07-30 08:31:46,696 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 混合比例 -> 混合比例 (置信度: 1.00)
2025-07-30 08:31:46,696 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 混合粘度 -> 混合粘度 (置信度: 1.00)
2025-07-30 08:31:46,696 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 混合密度 -> 固化后密度 (置信度: 1.00)
2025-07-30 08:31:46,696 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 凝胶时间 -> 不流动时间 (置信度: 1.00)
2025-07-30 08:31:46,696 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 固化时间* -> 固化时间 (置信度: 1.00)
2025-07-30 08:31:46,696 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 硬度 -> 硬度 (置信度: 1.00)
2025-07-30 08:31:46,696 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 体积电阻率 -> 体积电阻率 (置信度: 1.00)
2025-07-30 08:31:46,696 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 绝缘强度 -> 绝缘强度 (置信度: 1.00)
2025-07-30 08:31:46,697 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 吸水率 -> 吸水率 (置信度: 1.00)
2025-07-30 08:31:46,697 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 拉伸强度 -> 拉伸强度 (置信度: 1.00)
2025-07-30 08:31:46,697 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 双85测试 -> 双85测试 (置信度: 0.75)
2025-07-30 08:31:46,697 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 未找到匹配的参数: 包装规格
2025-07-30 08:31:46,697 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 未找到匹配的参数: 规格1
2025-07-30 08:31:46,697 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 未找到匹配的参数: 规格2
2025-07-30 08:31:46,697 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 未找到匹配的参数: 规格3
2025-07-30 08:31:46,697 - src.summary_table.parameter_mapper - INFO - logger.py:76 - 参数映射完成，映射了 19 个参数
2025-07-30 08:48:50,710 - src.utils.config_manager - INFO - logger.py:76 - 成功加载配置文件: config.json
2025-07-30 08:48:50,722 - src.utils.config_manager - INFO - logger.py:76 - 加载学习映射: 7 个映射
2025-07-30 08:48:55,905 - src.extraction.base_extractor - INFO - logger.py:76 - Word提取器初始化完成
2025-07-30 08:48:55,906 - src.extraction.base_extractor - INFO - logger.py:76 - 开始提取Word内容: FM-109-17技术参数表20250101.docx
2025-07-30 08:48:56,061 - src.extraction.base_extractor - DEBUG - logger.py:72 - 提取嵌套表格: 5 行数据
2025-07-30 08:48:56,063 - src.extraction.base_extractor - DEBUG - logger.py:72 - 提取嵌套表格: 6 行数据
2025-07-30 08:48:56,065 - src.extraction.base_extractor - DEBUG - logger.py:72 - 提取嵌套表格: 8 行数据
2025-07-30 08:48:56,067 - src.extraction.base_extractor - DEBUG - logger.py:72 - 提取嵌套表格: 3 行数据
2025-07-30 08:48:56,069 - src.extraction.base_extractor - DEBUG - logger.py:72 - 从文件名提取产品名称: FM-109-17
2025-07-30 08:48:56,069 - src.extraction.base_extractor - INFO - logger.py:76 - Word内容提取完成: 2 个表格, 0 个段落
2025-07-30 08:48:56,083 - src.summary_table.config_manager - INFO - logger.py:76 - 成功加载配置文件: config.json
2025-07-30 08:48:56,083 - src.extraction.base_extractor - INFO - logger.py:76 - PDF提取器初始化完成
2025-07-30 08:48:56,084 - src.extraction.base_extractor - INFO - logger.py:76 - Word提取器初始化完成
2025-07-30 08:48:56,084 - src.summary_table.parameter_mapper - INFO - logger.py:76 - 参数映射器初始化完成，支持 34 个参数
2025-07-30 08:48:56,084 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - A/B组分提取器初始化完成
2025-07-30 08:48:56,084 - src.summary_table.data_extractor - INFO - logger.py:76 - 技术数据提取器初始化完成
2025-07-30 08:48:56,084 - src.summary_table.data_extractor - INFO - logger.py:76 - 开始提取文件: FM-109-17技术参数表20250101.docx
2025-07-30 08:48:56,085 - src.extraction.base_extractor - INFO - logger.py:76 - 开始提取Word内容: FM-109-17技术参数表20250101.docx
2025-07-30 08:48:56,094 - src.extraction.base_extractor - DEBUG - logger.py:72 - 提取嵌套表格: 5 行数据
2025-07-30 08:48:56,096 - src.extraction.base_extractor - DEBUG - logger.py:72 - 提取嵌套表格: 6 行数据
2025-07-30 08:48:56,098 - src.extraction.base_extractor - DEBUG - logger.py:72 - 提取嵌套表格: 8 行数据
2025-07-30 08:48:56,100 - src.extraction.base_extractor - DEBUG - logger.py:72 - 提取嵌套表格: 3 行数据
2025-07-30 08:48:56,101 - src.extraction.base_extractor - DEBUG - logger.py:72 - 从文件名提取产品名称: FM-109-17
2025-07-30 08:48:56,101 - src.extraction.base_extractor - INFO - logger.py:76 - Word内容提取完成: 2 个表格, 0 个段落
2025-07-30 08:48:56,102 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - A组分列匹配: '109-17A' -> 模式: ^.*A$
2025-07-30 08:48:56,102 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - B组分列匹配: '109-17B' -> 模式: ^.*B$
2025-07-30 08:48:56,102 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到有效A/B列: A列=2('109-17A'), B列=3('109-17B')
2025-07-30 08:48:56,102 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 从数据行找到A/B列: (2, 3)
2025-07-30 08:48:56,102 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到核心参数: 外观 在 '外观'
2025-07-30 08:48:56,102 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到核心参数: 粘度 在 '粘度'
2025-07-30 08:48:56,102 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到核心参数: 密度 在 '密度'
2025-07-30 08:48:56,102 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - A/B组分表格验证通过，找到 3 个核心参数，1 个可选参数，外观参数: 是
2025-07-30 08:48:56,102 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到A/B组分表格，页面: 1
2025-07-30 08:48:56,102 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - A组分列匹配: '109-17A' -> 模式: ^.*A$
2025-07-30 08:48:56,102 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - B组分列匹配: '109-17B' -> 模式: ^.*B$
2025-07-30 08:48:56,103 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到有效A/B列: A列=2('109-17A'), B列=3('109-17B')
2025-07-30 08:48:56,103 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 从数据行找到A/B列: (2, 3)
2025-07-30 08:48:56,103 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 检查A组分数据: '外观' -> 列2 = '黄色透明液体', 是否为单位: False
2025-07-30 08:48:56,103 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - ✓ 提取A组分: 外观(A) = 黄色透明液体
2025-07-30 08:48:56,103 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 检查B组分数据: '外观' -> 列3 = '黄色透明/黑色液体', 是否为单位: False
2025-07-30 08:48:56,103 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - ✓ 提取B组分: 外观(B) = 黄色透明/黑色液体
2025-07-30 08:48:56,103 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 检查A组分数据: '粘度' -> 列2 = '400±200', 是否为单位: False
2025-07-30 08:48:56,103 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - ✓ 提取A组分: 粘度(A) = 400±200
2025-07-30 08:48:56,104 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 检查B组分数据: '粘度' -> 列3 = '700±200', 是否为单位: False
2025-07-30 08:48:56,104 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - ✓ 提取B组分: 粘度(B) = 700±200
2025-07-30 08:48:56,104 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 检查A组分数据: '密度' -> 列2 = '1.10±0.05', 是否为单位: False
2025-07-30 08:48:56,104 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - ✓ 提取A组分: 密度(A) = 1.10±0.05
2025-07-30 08:48:56,105 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 检查B组分数据: '密度' -> 列3 = '0.96±0.05', 是否为单位: False
2025-07-30 08:48:56,105 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - ✓ 提取B组分: 密度(B) = 0.96±0.05
2025-07-30 08:48:56,105 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 检查A组分数据: '保存期限' -> 列2 = '6个月', 是否为单位: False
2025-07-30 08:48:56,105 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - ✓ 提取A组分: 保存期限(A) = 6个月
2025-07-30 08:48:56,105 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 检查B组分数据: '保存期限' -> 列3 = '6个月', 是否为单位: False
2025-07-30 08:48:56,105 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - ✓ 提取B组分: 保存期限(B) = 6个月
2025-07-30 08:48:56,106 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 未匹配参数: '混合比例' -> A: '100:100', B: '100:100')
2025-07-30 08:48:56,106 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 跳过重复A组分: 粘度(A) (已存在: 400±200)
2025-07-30 08:48:56,106 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 跳过重复B组分: 粘度(B) (已存在: 700±200)
2025-07-30 08:48:56,106 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 跳过重复A组分: 密度(A) (已存在: 1.10±0.05)
2025-07-30 08:48:56,106 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 跳过重复B组分: 密度(B) (已存在: 0.96±0.05)
2025-07-30 08:48:56,106 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 未匹配参数: '凝胶时间' -> A: '90~150(100g)', B: '30~40(20g)')
2025-07-30 08:48:56,106 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 未匹配参数: '固化时间*' -> A: '60/3~6 | 23/48~72', B: '60/3~6 | 23/48~72')
2025-07-30 08:48:56,106 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 未匹配参数: '硬度' -> A: '35±10', B: 'GB/T 531.1-2008')
2025-07-30 08:48:56,106 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 未匹配参数: '体积电阻率' -> A: '>1011', B: 'GB/T 1692-2008')
2025-07-30 08:48:56,106 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 未匹配参数: '绝缘强度' -> A: '>15', B: 'GB/T 1408.1-2016')
2025-07-30 08:48:56,106 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 未匹配参数: '吸水率' -> A: '＜0.3', B: '企业标准')
2025-07-30 08:48:56,106 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 未匹配参数: '拉伸强度' -> A: '>0.3', B: 'GB/T 528-2009')
2025-07-30 08:48:56,106 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 未匹配参数: '双85测试' -> A: '＞500', B: '企业标准')
2025-07-30 08:48:56,106 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 未匹配参数: '阻燃等级' -> A: 'V-0', B: 'UL-94')
2025-07-30 08:48:56,106 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 未匹配参数: '应用温度范围' -> A: '-60~100', B: '企业标准')
2025-07-30 08:48:56,106 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - A/B组分提取完成，提取到 8 个参数
2025-07-30 08:48:56,107 - src.summary_table.data_extractor - INFO - logger.py:76 - A/B组分提取器提取到 8 个参数
2025-07-30 08:48:56,107 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - A组分列匹配: '109-17A' -> 模式: ^.*A$
2025-07-30 08:48:56,107 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - B组分列匹配: '109-17B' -> 模式: ^.*B$
2025-07-30 08:48:56,107 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到有效A/B列: A列=2('109-17A'), B列=3('109-17B')
2025-07-30 08:48:56,107 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 从数据行找到A/B列: (2, 3)
2025-07-30 08:48:56,107 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到核心参数: 外观 在 '外观'
2025-07-30 08:48:56,107 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到核心参数: 粘度 在 '粘度'
2025-07-30 08:48:56,107 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到核心参数: 密度 在 '密度'
2025-07-30 08:48:56,107 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - A/B组分表格验证通过，找到 3 个核心参数，1 个可选参数，外观参数: 是
2025-07-30 08:48:56,107 - src.summary_table.data_extractor - DEBUG - logger.py:72 - 从表头提取产品描述: FM-109-17是一款阻燃型双组份聚氨酯密封胶，符合环保要求，外观透明、硬度适中，防水性能和绝缘性...
2025-07-30 08:48:56,107 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - A组分列匹配: '109-17A' -> 模式: ^.*A$
2025-07-30 08:48:56,107 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - B组分列匹配: '109-17B' -> 模式: ^.*B$
2025-07-30 08:48:56,107 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到有效A/B列: A列=2('109-17A'), B列=3('109-17B')
2025-07-30 08:48:56,107 - src.summary_table.data_extractor - DEBUG - logger.py:72 - 提取非A/B参数: 混合比例 = 100:100
2025-07-30 08:48:56,107 - src.summary_table.data_extractor - DEBUG - logger.py:72 - 提取非A/B参数: 混合粘度 = 400±200
2025-07-30 08:48:56,107 - src.summary_table.data_extractor - DEBUG - logger.py:72 - 提取非A/B参数: 混合密度 = 1.05±0.05
2025-07-30 08:48:56,107 - src.summary_table.data_extractor - DEBUG - logger.py:72 - 提取非A/B参数: 凝胶时间 = 90~150(100g)
2025-07-30 08:48:56,107 - src.summary_table.data_extractor - DEBUG - logger.py:72 - 提取非A/B参数: 固化时间* = 60/3~6 | 23/48~72
2025-07-30 08:48:56,107 - src.summary_table.data_extractor - DEBUG - logger.py:72 - 提取非A/B参数: 硬度 = 35±10
2025-07-30 08:48:56,107 - src.summary_table.data_extractor - DEBUG - logger.py:72 - 提取非A/B参数: 体积电阻率 = >1011
2025-07-30 08:48:56,107 - src.summary_table.data_extractor - DEBUG - logger.py:72 - 提取非A/B参数: 绝缘强度 = >15
2025-07-30 08:48:56,107 - src.summary_table.data_extractor - DEBUG - logger.py:72 - 提取非A/B参数: 吸水率 = ＜0.3
2025-07-30 08:48:56,108 - src.summary_table.data_extractor - DEBUG - logger.py:72 - 提取非A/B参数: 拉伸强度 = >0.3
2025-07-30 08:48:56,108 - src.summary_table.data_extractor - DEBUG - logger.py:72 - 提取非A/B参数: 双85测试 = ＞500
2025-07-30 08:48:56,108 - src.summary_table.data_extractor - DEBUG - logger.py:72 - 提取非A/B参数: 阻燃等级 = V-0
2025-07-30 08:48:56,108 - src.summary_table.data_extractor - DEBUG - logger.py:72 - 提取非A/B参数: 应用温度范围 = -60~100
2025-07-30 08:48:56,108 - src.summary_table.data_extractor - DEBUG - logger.py:72 - 从A/B组分表格提取非A/B参数: 14 个
2025-07-30 08:48:56,109 - src.summary_table.data_extractor - DEBUG - logger.py:72 - 从普通表格提取参数: 6 个
2025-07-30 08:48:56,109 - src.summary_table.data_extractor - DEBUG - logger.py:72 - 总共提取到 28 个原始参数
2025-07-30 08:48:56,110 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 保留A/B组分参数: 外观(A) = 黄色透明液体
2025-07-30 08:48:56,110 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 保留A/B组分参数: 外观(B) = 黄色透明/黑色液体
2025-07-30 08:48:56,110 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 保留A/B组分参数: 粘度(A) = 400±200
2025-07-30 08:48:56,110 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 保留A/B组分参数: 粘度(B) = 700±200
2025-07-30 08:48:56,110 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 保留A/B组分参数: 密度(A) = 1.10±0.05
2025-07-30 08:48:56,110 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 保留A/B组分参数: 密度(B) = 0.96±0.05
2025-07-30 08:48:56,110 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 保留A/B组分参数: 保存期限(A) = 6个月
2025-07-30 08:48:56,110 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 保留A/B组分参数: 保存期限(B) = 6个月
2025-07-30 08:48:56,110 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 产品描述 -> 性能特点 (置信度: 1.00)
2025-07-30 08:48:56,110 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 混合比例 -> 混合比例 (置信度: 1.00)
2025-07-30 08:48:56,110 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 混合粘度 -> 混合粘度 (置信度: 1.00)
2025-07-30 08:48:56,110 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 混合密度 -> 固化后密度 (置信度: 1.00)
2025-07-30 08:48:56,110 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 凝胶时间 -> 不流动时间 (置信度: 1.00)
2025-07-30 08:48:56,110 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 固化时间* -> 固化时间 (置信度: 0.89)
2025-07-30 08:48:56,110 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 硬度 -> 硬度 (置信度: 1.00)
2025-07-30 08:48:56,110 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 体积电阻率 -> 体积电阻率 (置信度: 1.00)
2025-07-30 08:48:56,110 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 绝缘强度 -> 绝缘强度 (置信度: 1.00)
2025-07-30 08:48:56,110 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 吸水率 -> 吸水率 (置信度: 1.00)
2025-07-30 08:48:56,110 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 拉伸强度 -> 拉伸强度 (置信度: 1.00)
2025-07-30 08:48:56,111 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 双85测试 -> 双85测试 (置信度: 1.00)
2025-07-30 08:48:56,111 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 阻燃等级 -> 阻燃 (置信度: 1.00)
2025-07-30 08:48:56,111 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 应用温度范围 -> 应用温度 (置信度: 1.00)
2025-07-30 08:48:56,111 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 未找到匹配的参数: 推荐工艺
2025-07-30 08:48:56,111 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 未找到匹配的参数: 包装规格
2025-07-30 08:48:56,111 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 未找到匹配的参数: 规格1
2025-07-30 08:48:56,111 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 未找到匹配的参数: 规格2
2025-07-30 08:48:56,111 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 未找到匹配的参数: 储存运输
2025-07-30 08:48:56,111 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 未找到匹配的参数: 重要说明
2025-07-30 08:48:56,111 - src.summary_table.parameter_mapper - INFO - logger.py:76 - 参数映射完成，映射了 22 个参数
2025-07-30 08:48:56,112 - src.validation.data_validator - INFO - logger.py:76 - 修复参数值: 外观(B) = 黄色透明/黑色液体 -> 黄色透明液体
2025-07-30 08:48:56,112 - src.validation.data_validator - WARNING - logger.py:80 - 参数格式异常: 体积电阻率 = >1011
2025-07-30 08:48:56,113 - src.validation.data_validator - INFO - logger.py:76 - 数据验证和修复完成
2025-07-30 08:48:56,113 - src.summary_table.data_extractor - INFO - logger.py:76 - Word提取完成: 22 个参数
2025-07-30 08:48:56,117 - src.summary_table.config_manager - INFO - logger.py:76 - 成功加载配置文件: config.json
2025-07-30 08:48:56,118 - src.extraction.base_extractor - INFO - logger.py:76 - PDF提取器初始化完成
2025-07-30 08:48:56,118 - src.extraction.base_extractor - INFO - logger.py:76 - Word提取器初始化完成
2025-07-30 08:48:56,118 - src.summary_table.parameter_mapper - INFO - logger.py:76 - 参数映射器初始化完成，支持 34 个参数
2025-07-30 08:48:56,119 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - A/B组分提取器初始化完成
2025-07-30 08:48:56,119 - src.summary_table.data_extractor - INFO - logger.py:76 - 技术数据提取器初始化完成
2025-07-30 08:48:56,119 - src.extraction.base_extractor - INFO - logger.py:76 - Word提取器初始化完成
2025-07-30 08:48:56,119 - src.extraction.base_extractor - INFO - logger.py:76 - 开始提取Word内容: FM-109-17技术参数表20250101.docx
2025-07-30 08:48:56,131 - src.extraction.base_extractor - DEBUG - logger.py:72 - 提取嵌套表格: 5 行数据
2025-07-30 08:48:56,133 - src.extraction.base_extractor - DEBUG - logger.py:72 - 提取嵌套表格: 6 行数据
2025-07-30 08:48:56,136 - src.extraction.base_extractor - DEBUG - logger.py:72 - 提取嵌套表格: 8 行数据
2025-07-30 08:48:56,138 - src.extraction.base_extractor - DEBUG - logger.py:72 - 提取嵌套表格: 3 行数据
2025-07-30 08:48:56,139 - src.extraction.base_extractor - DEBUG - logger.py:72 - 从文件名提取产品名称: FM-109-17
2025-07-30 08:48:56,139 - src.extraction.base_extractor - INFO - logger.py:76 - Word内容提取完成: 2 个表格, 0 个段落
2025-07-30 08:48:56,140 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - A组分列匹配: '109-17A' -> 模式: ^.*A$
2025-07-30 08:48:56,140 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - B组分列匹配: '109-17B' -> 模式: ^.*B$
2025-07-30 08:48:56,140 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到有效A/B列: A列=2('109-17A'), B列=3('109-17B')
2025-07-30 08:48:56,140 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 从数据行找到A/B列: (2, 3)
2025-07-30 08:48:56,140 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到核心参数: 外观 在 '外观'
2025-07-30 08:48:56,140 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到核心参数: 粘度 在 '粘度'
2025-07-30 08:48:56,140 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到核心参数: 密度 在 '密度'
2025-07-30 08:48:56,140 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - A/B组分表格验证通过，找到 3 个核心参数，1 个可选参数，外观参数: 是
2025-07-30 08:48:56,140 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到A/B组分表格，页面: 1
2025-07-30 08:48:56,140 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - A组分列匹配: '109-17A' -> 模式: ^.*A$
2025-07-30 08:48:56,140 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - B组分列匹配: '109-17B' -> 模式: ^.*B$
2025-07-30 08:48:56,140 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到有效A/B列: A列=2('109-17A'), B列=3('109-17B')
2025-07-30 08:48:56,140 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 从数据行找到A/B列: (2, 3)
2025-07-30 08:48:56,140 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 检查A组分数据: '外观' -> 列2 = '黄色透明液体', 是否为单位: False
2025-07-30 08:48:56,140 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - ✓ 提取A组分: 外观(A) = 黄色透明液体
2025-07-30 08:48:56,140 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 检查B组分数据: '外观' -> 列3 = '黄色透明/黑色液体', 是否为单位: False
2025-07-30 08:48:56,141 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - ✓ 提取B组分: 外观(B) = 黄色透明/黑色液体
2025-07-30 08:48:56,141 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 检查A组分数据: '粘度' -> 列2 = '400±200', 是否为单位: False
2025-07-30 08:48:56,141 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - ✓ 提取A组分: 粘度(A) = 400±200
2025-07-30 08:48:56,141 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 检查B组分数据: '粘度' -> 列3 = '700±200', 是否为单位: False
2025-07-30 08:48:56,141 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - ✓ 提取B组分: 粘度(B) = 700±200
2025-07-30 08:48:56,141 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 检查A组分数据: '密度' -> 列2 = '1.10±0.05', 是否为单位: False
2025-07-30 08:48:56,141 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - ✓ 提取A组分: 密度(A) = 1.10±0.05
2025-07-30 08:48:56,141 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 检查B组分数据: '密度' -> 列3 = '0.96±0.05', 是否为单位: False
2025-07-30 08:48:56,141 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - ✓ 提取B组分: 密度(B) = 0.96±0.05
2025-07-30 08:48:56,141 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 检查A组分数据: '保存期限' -> 列2 = '6个月', 是否为单位: False
2025-07-30 08:48:56,141 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - ✓ 提取A组分: 保存期限(A) = 6个月
2025-07-30 08:48:56,141 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 检查B组分数据: '保存期限' -> 列3 = '6个月', 是否为单位: False
2025-07-30 08:48:56,141 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - ✓ 提取B组分: 保存期限(B) = 6个月
2025-07-30 08:48:56,143 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 未匹配参数: '混合比例' -> A: '100:100', B: '100:100')
2025-07-30 08:48:56,143 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 跳过重复A组分: 粘度(A) (已存在: 400±200)
2025-07-30 08:48:56,143 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 跳过重复B组分: 粘度(B) (已存在: 700±200)
2025-07-30 08:48:56,144 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 跳过重复A组分: 密度(A) (已存在: 1.10±0.05)
2025-07-30 08:48:56,144 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 跳过重复B组分: 密度(B) (已存在: 0.96±0.05)
2025-07-30 08:48:56,144 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 未匹配参数: '凝胶时间' -> A: '90~150(100g)', B: '30~40(20g)')
2025-07-30 08:48:56,144 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 未匹配参数: '固化时间*' -> A: '60/3~6 | 23/48~72', B: '60/3~6 | 23/48~72')
2025-07-30 08:48:56,144 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 未匹配参数: '硬度' -> A: '35±10', B: 'GB/T 531.1-2008')
2025-07-30 08:48:56,144 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 未匹配参数: '体积电阻率' -> A: '>1011', B: 'GB/T 1692-2008')
2025-07-30 08:48:56,144 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 未匹配参数: '绝缘强度' -> A: '>15', B: 'GB/T 1408.1-2016')
2025-07-30 08:48:56,144 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 未匹配参数: '吸水率' -> A: '＜0.3', B: '企业标准')
2025-07-30 08:48:56,144 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 未匹配参数: '拉伸强度' -> A: '>0.3', B: 'GB/T 528-2009')
2025-07-30 08:48:56,144 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 未匹配参数: '双85测试' -> A: '＞500', B: '企业标准')
2025-07-30 08:48:56,144 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 未匹配参数: '阻燃等级' -> A: 'V-0', B: 'UL-94')
2025-07-30 08:48:56,144 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 未匹配参数: '应用温度范围' -> A: '-60~100', B: '企业标准')
2025-07-30 08:48:56,144 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - A/B组分提取完成，提取到 8 个参数
2025-07-30 08:48:56,144 - src.summary_table.data_extractor - INFO - logger.py:76 - A/B组分提取器提取到 8 个参数
2025-07-30 08:48:56,145 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - A组分列匹配: '109-17A' -> 模式: ^.*A$
2025-07-30 08:48:56,145 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - B组分列匹配: '109-17B' -> 模式: ^.*B$
2025-07-30 08:48:56,145 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到有效A/B列: A列=2('109-17A'), B列=3('109-17B')
2025-07-30 08:48:56,145 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 从数据行找到A/B列: (2, 3)
2025-07-30 08:48:56,145 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到核心参数: 外观 在 '外观'
2025-07-30 08:48:56,145 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到核心参数: 粘度 在 '粘度'
2025-07-30 08:48:56,145 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到核心参数: 密度 在 '密度'
2025-07-30 08:48:56,145 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - A/B组分表格验证通过，找到 3 个核心参数，1 个可选参数，外观参数: 是
2025-07-30 08:48:56,145 - src.summary_table.data_extractor - DEBUG - logger.py:72 - 从表头提取产品描述: FM-109-17是一款阻燃型双组份聚氨酯密封胶，符合环保要求，外观透明、硬度适中，防水性能和绝缘性...
2025-07-30 08:48:56,145 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - A组分列匹配: '109-17A' -> 模式: ^.*A$
2025-07-30 08:48:56,145 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - B组分列匹配: '109-17B' -> 模式: ^.*B$
2025-07-30 08:48:56,145 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到有效A/B列: A列=2('109-17A'), B列=3('109-17B')
2025-07-30 08:48:56,145 - src.summary_table.data_extractor - DEBUG - logger.py:72 - 提取非A/B参数: 混合比例 = 100:100
2025-07-30 08:48:56,145 - src.summary_table.data_extractor - DEBUG - logger.py:72 - 提取非A/B参数: 混合粘度 = 400±200
2025-07-30 08:48:56,145 - src.summary_table.data_extractor - DEBUG - logger.py:72 - 提取非A/B参数: 混合密度 = 1.05±0.05
2025-07-30 08:48:56,145 - src.summary_table.data_extractor - DEBUG - logger.py:72 - 提取非A/B参数: 凝胶时间 = 90~150(100g)
2025-07-30 08:48:56,145 - src.summary_table.data_extractor - DEBUG - logger.py:72 - 提取非A/B参数: 固化时间* = 60/3~6 | 23/48~72
2025-07-30 08:48:56,145 - src.summary_table.data_extractor - DEBUG - logger.py:72 - 提取非A/B参数: 硬度 = 35±10
2025-07-30 08:48:56,145 - src.summary_table.data_extractor - DEBUG - logger.py:72 - 提取非A/B参数: 体积电阻率 = >1011
2025-07-30 08:48:56,145 - src.summary_table.data_extractor - DEBUG - logger.py:72 - 提取非A/B参数: 绝缘强度 = >15
2025-07-30 08:48:56,145 - src.summary_table.data_extractor - DEBUG - logger.py:72 - 提取非A/B参数: 吸水率 = ＜0.3
2025-07-30 08:48:56,145 - src.summary_table.data_extractor - DEBUG - logger.py:72 - 提取非A/B参数: 拉伸强度 = >0.3
2025-07-30 08:48:56,145 - src.summary_table.data_extractor - DEBUG - logger.py:72 - 提取非A/B参数: 双85测试 = ＞500
2025-07-30 08:48:56,145 - src.summary_table.data_extractor - DEBUG - logger.py:72 - 提取非A/B参数: 阻燃等级 = V-0
2025-07-30 08:48:56,145 - src.summary_table.data_extractor - DEBUG - logger.py:72 - 提取非A/B参数: 应用温度范围 = -60~100
2025-07-30 08:48:56,145 - src.summary_table.data_extractor - DEBUG - logger.py:72 - 从A/B组分表格提取非A/B参数: 14 个
2025-07-30 08:48:56,145 - src.summary_table.data_extractor - DEBUG - logger.py:72 - 从普通表格提取参数: 6 个
2025-07-30 08:48:56,145 - src.summary_table.data_extractor - DEBUG - logger.py:72 - 总共提取到 28 个原始参数
2025-07-30 08:48:56,147 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - A/B组分提取器初始化完成
2025-07-30 08:48:56,148 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - A组分列匹配: '109-17A' -> 模式: ^.*A$
2025-07-30 08:48:56,148 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - B组分列匹配: '109-17B' -> 模式: ^.*B$
2025-07-30 08:48:56,148 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到有效A/B列: A列=2('109-17A'), B列=3('109-17B')
2025-07-30 08:48:56,148 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 从数据行找到A/B列: (2, 3)
2025-07-30 08:48:56,148 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到核心参数: 外观 在 '外观'
2025-07-30 08:48:56,148 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到核心参数: 粘度 在 '粘度'
2025-07-30 08:48:56,148 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到核心参数: 密度 在 '密度'
2025-07-30 08:48:56,148 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - A/B组分表格验证通过，找到 3 个核心参数，1 个可选参数，外观参数: 是
2025-07-30 08:48:56,148 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到A/B组分表格，页面: 1
2025-07-30 08:48:56,148 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - A组分列匹配: '109-17A' -> 模式: ^.*A$
2025-07-30 08:48:56,148 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - B组分列匹配: '109-17B' -> 模式: ^.*B$
2025-07-30 08:48:56,148 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到有效A/B列: A列=2('109-17A'), B列=3('109-17B')
2025-07-30 08:48:56,148 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 从数据行找到A/B列: (2, 3)
2025-07-30 08:48:56,148 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到核心参数: 外观 在 '外观'
2025-07-30 08:48:56,148 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到核心参数: 粘度 在 '粘度'
2025-07-30 08:48:56,148 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到核心参数: 密度 在 '密度'
2025-07-30 08:48:56,148 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - A/B组分表格验证通过，找到 3 个核心参数，1 个可选参数，外观参数: 是
2025-07-30 08:48:56,148 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到A/B组分表格，页面: 1
2025-07-30 08:48:56,148 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - A组分列匹配: '109-17A' -> 模式: ^.*A$
2025-07-30 08:48:56,148 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - B组分列匹配: '109-17B' -> 模式: ^.*B$
2025-07-30 08:48:56,148 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到有效A/B列: A列=2('109-17A'), B列=3('109-17B')
2025-07-30 08:48:56,148 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 从数据行找到A/B列: (2, 3)
2025-07-30 08:48:56,148 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 检查A组分数据: '外观' -> 列2 = '黄色透明液体', 是否为单位: False
2025-07-30 08:48:56,148 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - ✓ 提取A组分: 外观(A) = 黄色透明液体
2025-07-30 08:48:56,148 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 检查B组分数据: '外观' -> 列3 = '黄色透明/黑色液体', 是否为单位: False
2025-07-30 08:48:56,149 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - ✓ 提取B组分: 外观(B) = 黄色透明/黑色液体
2025-07-30 08:48:56,149 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 检查A组分数据: '粘度' -> 列2 = '400±200', 是否为单位: False
2025-07-30 08:48:56,149 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - ✓ 提取A组分: 粘度(A) = 400±200
2025-07-30 08:48:56,149 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 检查B组分数据: '粘度' -> 列3 = '700±200', 是否为单位: False
2025-07-30 08:48:56,149 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - ✓ 提取B组分: 粘度(B) = 700±200
2025-07-30 08:48:56,149 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 检查A组分数据: '密度' -> 列2 = '1.10±0.05', 是否为单位: False
2025-07-30 08:48:56,149 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - ✓ 提取A组分: 密度(A) = 1.10±0.05
2025-07-30 08:48:56,149 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 检查B组分数据: '密度' -> 列3 = '0.96±0.05', 是否为单位: False
2025-07-30 08:48:56,149 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - ✓ 提取B组分: 密度(B) = 0.96±0.05
2025-07-30 08:48:56,149 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 检查A组分数据: '保存期限' -> 列2 = '6个月', 是否为单位: False
2025-07-30 08:48:56,149 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - ✓ 提取A组分: 保存期限(A) = 6个月
2025-07-30 08:48:56,150 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 检查B组分数据: '保存期限' -> 列3 = '6个月', 是否为单位: False
2025-07-30 08:48:56,150 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - ✓ 提取B组分: 保存期限(B) = 6个月
2025-07-30 08:48:56,150 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 未匹配参数: '混合比例' -> A: '100:100', B: '100:100')
2025-07-30 08:48:56,150 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 跳过重复A组分: 粘度(A) (已存在: 400±200)
2025-07-30 08:48:56,150 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 跳过重复B组分: 粘度(B) (已存在: 700±200)
2025-07-30 08:48:56,150 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 跳过重复A组分: 密度(A) (已存在: 1.10±0.05)
2025-07-30 08:48:56,150 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 跳过重复B组分: 密度(B) (已存在: 0.96±0.05)
2025-07-30 08:48:56,150 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 未匹配参数: '凝胶时间' -> A: '90~150(100g)', B: '30~40(20g)')
2025-07-30 08:48:56,150 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 未匹配参数: '固化时间*' -> A: '60/3~6 | 23/48~72', B: '60/3~6 | 23/48~72')
2025-07-30 08:48:56,150 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 未匹配参数: '硬度' -> A: '35±10', B: 'GB/T 531.1-2008')
2025-07-30 08:48:56,150 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 未匹配参数: '体积电阻率' -> A: '>1011', B: 'GB/T 1692-2008')
2025-07-30 08:48:56,150 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 未匹配参数: '绝缘强度' -> A: '>15', B: 'GB/T 1408.1-2016')
2025-07-30 08:48:56,150 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 未匹配参数: '吸水率' -> A: '＜0.3', B: '企业标准')
2025-07-30 08:48:56,150 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 未匹配参数: '拉伸强度' -> A: '>0.3', B: 'GB/T 528-2009')
2025-07-30 08:48:56,150 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 未匹配参数: '双85测试' -> A: '＞500', B: '企业标准')
2025-07-30 08:48:56,150 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 未匹配参数: '阻燃等级' -> A: 'V-0', B: 'UL-94')
2025-07-30 08:48:56,150 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 未匹配参数: '应用温度范围' -> A: '-60~100', B: '企业标准')
2025-07-30 08:48:56,150 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - A/B组分提取完成，提取到 8 个参数
2025-07-30 08:48:56,151 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - A组分列匹配: '109-17A' -> 模式: ^.*A$
2025-07-30 08:48:56,151 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - B组分列匹配: '109-17B' -> 模式: ^.*B$
2025-07-30 08:48:56,151 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到有效A/B列: A列=2('109-17A'), B列=3('109-17B')
2025-07-30 08:48:56,151 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 从数据行找到A/B列: (2, 3)
2025-07-30 08:48:56,151 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到核心参数: 外观 在 '外观'
2025-07-30 08:48:56,151 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到核心参数: 粘度 在 '粘度'
2025-07-30 08:48:56,151 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到核心参数: 密度 在 '密度'
2025-07-30 08:48:56,151 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - A/B组分表格验证通过，找到 3 个核心参数，1 个可选参数，外观参数: 是
2025-07-30 08:48:56,151 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到A/B组分表格，页面: 1
2025-07-30 08:48:56,151 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - A组分列匹配: '109-17A' -> 模式: ^.*A$
2025-07-30 08:48:56,151 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - B组分列匹配: '109-17B' -> 模式: ^.*B$
2025-07-30 08:48:56,151 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到有效A/B列: A列=2('109-17A'), B列=3('109-17B')
2025-07-30 08:48:56,151 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 保留A/B组分参数: 外观(A) = 黄色透明液体
2025-07-30 08:48:56,152 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 保留A/B组分参数: 外观(B) = 黄色透明/黑色液体
2025-07-30 08:48:56,152 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 保留A/B组分参数: 粘度(A) = 400±200
2025-07-30 08:48:56,152 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 保留A/B组分参数: 粘度(B) = 700±200
2025-07-30 08:48:56,152 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 保留A/B组分参数: 密度(A) = 1.10±0.05
2025-07-30 08:48:56,152 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 保留A/B组分参数: 密度(B) = 0.96±0.05
2025-07-30 08:48:56,152 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 保留A/B组分参数: 保存期限(A) = 6个月
2025-07-30 08:48:56,152 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 保留A/B组分参数: 保存期限(B) = 6个月
2025-07-30 08:48:56,152 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 产品描述 -> 性能特点 (置信度: 1.00)
2025-07-30 08:48:56,152 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 混合比例 -> 混合比例 (置信度: 1.00)
2025-07-30 08:48:56,152 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 混合粘度 -> 混合粘度 (置信度: 1.00)
2025-07-30 08:48:56,152 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 混合密度 -> 固化后密度 (置信度: 1.00)
2025-07-30 08:48:56,152 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 凝胶时间 -> 不流动时间 (置信度: 1.00)
2025-07-30 08:48:56,152 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 固化时间* -> 固化时间 (置信度: 0.89)
2025-07-30 08:48:56,152 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 硬度 -> 硬度 (置信度: 1.00)
2025-07-30 08:48:56,152 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 体积电阻率 -> 体积电阻率 (置信度: 1.00)
2025-07-30 08:48:56,152 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 绝缘强度 -> 绝缘强度 (置信度: 1.00)
2025-07-30 08:48:56,152 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 吸水率 -> 吸水率 (置信度: 1.00)
2025-07-30 08:48:56,152 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 拉伸强度 -> 拉伸强度 (置信度: 1.00)
2025-07-30 08:48:56,152 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 双85测试 -> 双85测试 (置信度: 1.00)
2025-07-30 08:48:56,152 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 阻燃等级 -> 阻燃 (置信度: 1.00)
2025-07-30 08:48:56,152 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 应用温度范围 -> 应用温度 (置信度: 1.00)
2025-07-30 08:48:56,152 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 未找到匹配的参数: 推荐工艺
2025-07-30 08:48:56,152 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 未找到匹配的参数: 包装规格
2025-07-30 08:48:56,152 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 未找到匹配的参数: 规格1
2025-07-30 08:48:56,152 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 未找到匹配的参数: 规格2
2025-07-30 08:48:56,152 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 未找到匹配的参数: 储存运输
2025-07-30 08:48:56,152 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 未找到匹配的参数: 重要说明
2025-07-30 08:48:56,153 - src.summary_table.parameter_mapper - INFO - logger.py:76 - 参数映射完成，映射了 22 个参数
2025-07-30 08:49:02,259 - src.utils.config_manager - INFO - logger.py:76 - 成功加载配置文件: config.json
2025-07-30 08:49:02,259 - src.utils.config_manager - INFO - logger.py:76 - 加载学习映射: 7 个映射
2025-07-30 08:49:03,512 - src.extraction.base_extractor - INFO - logger.py:76 - PDF提取器初始化完成
2025-07-30 08:49:03,513 - src.extraction.base_extractor - INFO - logger.py:76 - 开始提取PDF内容: FM-109-17技术参数表.pdf
2025-07-30 08:49:03,542 - src.extraction.base_extractor - DEBUG - logger.py:72 - 处理第 1 页
2025-07-30 08:49:03,690 - src.extraction.base_extractor - DEBUG - logger.py:72 - 第 1 页提取到表格 1: 4 行
2025-07-30 08:49:03,691 - src.extraction.base_extractor - DEBUG - logger.py:72 - 第 1 页提取到表格 2: 5 行
2025-07-30 08:49:03,691 - src.extraction.base_extractor - DEBUG - logger.py:72 - 第 1 页提取到表格 3: 8 行
2025-07-30 08:49:03,693 - src.extraction.base_extractor - DEBUG - logger.py:72 - 处理第 2 页
2025-07-30 08:49:03,806 - src.extraction.base_extractor - DEBUG - logger.py:72 - 第 2 页提取到表格 1: 4 行
2025-07-30 08:49:03,809 - src.extraction.base_extractor - DEBUG - logger.py:72 - 合并表格 1 和 2
2025-07-30 08:49:03,809 - src.extraction.base_extractor - INFO - logger.py:76 - 表格合并完成: 4 -> 3
2025-07-30 08:49:03,816 - src.extraction.base_extractor - DEBUG - logger.py:72 - 从页眉提取产品名称: FM-109-17
2025-07-30 08:49:03,816 - src.extraction.base_extractor - INFO - logger.py:76 - PDF内容提取完成: 3 个表格, 66 个段落
2025-07-30 08:49:03,820 - src.summary_table.config_manager - INFO - logger.py:76 - 成功加载配置文件: config.json
2025-07-30 08:49:03,820 - src.extraction.base_extractor - INFO - logger.py:76 - PDF提取器初始化完成
2025-07-30 08:49:03,821 - src.extraction.base_extractor - INFO - logger.py:76 - Word提取器初始化完成
2025-07-30 08:49:03,821 - src.summary_table.parameter_mapper - INFO - logger.py:76 - 参数映射器初始化完成，支持 34 个参数
2025-07-30 08:49:03,822 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - A/B组分提取器初始化完成
2025-07-30 08:49:03,822 - src.summary_table.data_extractor - INFO - logger.py:76 - 技术数据提取器初始化完成
2025-07-30 08:49:03,822 - src.summary_table.data_extractor - INFO - logger.py:76 - 开始提取文件: FM-109-17技术参数表.pdf
2025-07-30 08:49:03,822 - src.extraction.base_extractor - INFO - logger.py:76 - 开始提取PDF内容: FM-109-17技术参数表.pdf
2025-07-30 08:49:03,825 - src.extraction.base_extractor - DEBUG - logger.py:72 - 处理第 1 页
2025-07-30 08:49:03,976 - src.extraction.base_extractor - DEBUG - logger.py:72 - 第 1 页提取到表格 1: 4 行
2025-07-30 08:49:03,976 - src.extraction.base_extractor - DEBUG - logger.py:72 - 第 1 页提取到表格 2: 5 行
2025-07-30 08:49:03,976 - src.extraction.base_extractor - DEBUG - logger.py:72 - 第 1 页提取到表格 3: 8 行
2025-07-30 08:49:03,978 - src.extraction.base_extractor - DEBUG - logger.py:72 - 处理第 2 页
2025-07-30 08:49:04,070 - src.extraction.base_extractor - DEBUG - logger.py:72 - 第 2 页提取到表格 1: 4 行
2025-07-30 08:49:04,072 - src.extraction.base_extractor - DEBUG - logger.py:72 - 合并表格 1 和 2
2025-07-30 08:49:04,073 - src.extraction.base_extractor - INFO - logger.py:76 - 表格合并完成: 4 -> 3
2025-07-30 08:49:04,073 - src.extraction.base_extractor - DEBUG - logger.py:72 - 从页眉提取产品名称: FM-109-17
2025-07-30 08:49:04,073 - src.extraction.base_extractor - INFO - logger.py:76 - PDF内容提取完成: 3 个表格, 66 个段落
2025-07-30 08:49:04,074 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - A组分列匹配: '109-17A' -> 模式: ^.*A$
2025-07-30 08:49:04,074 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - B组分列匹配: '109-17B' -> 模式: ^.*B$
2025-07-30 08:49:04,074 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到有效A/B列: A列=2('109-17A'), B列=3('109-17B')
2025-07-30 08:49:04,074 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 从表头找到A/B列: (2, 3)
2025-07-30 08:49:04,076 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到核心参数: 外观 在 '外观'
2025-07-30 08:49:04,076 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到核心参数: 粘度 在 '粘度'
2025-07-30 08:49:04,076 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到核心参数: 密度 在 '密度'
2025-07-30 08:49:04,076 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - A/B组分表格验证通过，找到 3 个核心参数，1 个可选参数，外观参数: 是
2025-07-30 08:49:04,076 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到A/B组分表格，页面: 1
2025-07-30 08:49:04,076 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - A组分列匹配: '109-17A' -> 模式: ^.*A$
2025-07-30 08:49:04,076 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - B组分列匹配: '109-17B' -> 模式: ^.*B$
2025-07-30 08:49:04,076 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到有效A/B列: A列=2('109-17A'), B列=3('109-17B')
2025-07-30 08:49:04,076 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 从表头找到A/B列: (2, 3)
2025-07-30 08:49:04,076 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 检查A组分数据: '外观' -> 列2 = '黄色透明液体', 是否为单位: False
2025-07-30 08:49:04,076 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - ✓ 提取A组分: 外观(A) = 黄色透明液体
2025-07-30 08:49:04,076 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 检查B组分数据: '外观' -> 列3 = '黄色透明/黑色液体', 是否为单位: False
2025-07-30 08:49:04,076 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - ✓ 提取B组分: 外观(B) = 黄色透明/黑色液体
2025-07-30 08:49:04,076 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 检查A组分数据: '粘度' -> 列2 = '400±200', 是否为单位: False
2025-07-30 08:49:04,076 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - ✓ 提取A组分: 粘度(A) = 400±200
2025-07-30 08:49:04,076 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 检查B组分数据: '粘度' -> 列3 = '700±200', 是否为单位: False
2025-07-30 08:49:04,076 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - ✓ 提取B组分: 粘度(B) = 700±200
2025-07-30 08:49:04,076 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 检查A组分数据: '密度' -> 列2 = '1.10±0.05', 是否为单位: False
2025-07-30 08:49:04,076 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - ✓ 提取A组分: 密度(A) = 1.10±0.05
2025-07-30 08:49:04,077 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 检查B组分数据: '密度' -> 列3 = '0.96±0.05', 是否为单位: False
2025-07-30 08:49:04,077 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - ✓ 提取B组分: 密度(B) = 0.96±0.05
2025-07-30 08:49:04,078 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 检查A组分数据: '保存期限' -> 列2 = '6个月', 是否为单位: False
2025-07-30 08:49:04,078 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - ✓ 提取A组分: 保存期限(A) = 6个月
2025-07-30 08:49:04,078 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 检查B组分数据: '保存期限' -> 列3 = '6个月', 是否为单位: False
2025-07-30 08:49:04,078 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - ✓ 提取B组分: 保存期限(B) = 6个月
2025-07-30 08:49:04,078 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 未匹配参数: '混合比例' -> A: '100:100', B: '')
2025-07-30 08:49:04,078 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 跳过重复A组分: 粘度(A) (已存在: 400±200)
2025-07-30 08:49:04,078 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 跳过重复B组分: 粘度(B) (已存在: 700±200)
2025-07-30 08:49:04,078 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 跳过重复A组分: 密度(A) (已存在: 1.10±0.05)
2025-07-30 08:49:04,078 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 跳过重复B组分: 密度(B) (已存在: 0.96±0.05)
2025-07-30 08:49:04,078 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 未匹配参数: '凝胶时间' -> A: '90~150(100g)', B: '30~40(20g)')
2025-07-30 08:49:04,078 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 未匹配参数: '固化时间*' -> A: '60/3~6 | 23/48~72', B: '')
2025-07-30 08:49:04,078 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - A/B组分提取完成，提取到 8 个参数
2025-07-30 08:49:04,078 - src.summary_table.data_extractor - INFO - logger.py:76 - A/B组分提取器提取到 8 个参数
2025-07-30 08:49:04,079 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - A组分列匹配: '109-17A' -> 模式: ^.*A$
2025-07-30 08:49:04,079 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - B组分列匹配: '109-17B' -> 模式: ^.*B$
2025-07-30 08:49:04,079 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到有效A/B列: A列=2('109-17A'), B列=3('109-17B')
2025-07-30 08:49:04,079 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 从表头找到A/B列: (2, 3)
2025-07-30 08:49:04,079 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到核心参数: 外观 在 '外观'
2025-07-30 08:49:04,079 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到核心参数: 粘度 在 '粘度'
2025-07-30 08:49:04,079 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到核心参数: 密度 在 '密度'
2025-07-30 08:49:04,079 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - A/B组分表格验证通过，找到 3 个核心参数，1 个可选参数，外观参数: 是
2025-07-30 08:49:04,079 - src.summary_table.data_extractor - DEBUG - logger.py:72 - 从A/B组分表格提取非A/B参数: 0 个
2025-07-30 08:49:04,081 - src.summary_table.data_extractor - DEBUG - logger.py:72 - 从普通表格提取参数: 6 个
2025-07-30 08:49:04,081 - src.summary_table.data_extractor - DEBUG - logger.py:72 - 从普通表格提取参数: 4 个
2025-07-30 08:49:04,083 - src.summary_table.data_extractor - DEBUG - logger.py:72 - 总共提取到 37 个原始参数
2025-07-30 08:49:04,083 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 保留A/B组分参数: 外观(A) = 黄色透明液体
2025-07-30 08:49:04,083 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 保留A/B组分参数: 外观(B) = 黄色透明/黑色液体
2025-07-30 08:49:04,083 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 保留A/B组分参数: 粘度(A) = 400±200
2025-07-30 08:49:04,083 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 保留A/B组分参数: 粘度(B) = 700±200
2025-07-30 08:49:04,083 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 保留A/B组分参数: 密度(A) = 1.10±0.05
2025-07-30 08:49:04,083 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 保留A/B组分参数: 密度(B) = 0.96±0.05
2025-07-30 08:49:04,083 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 保留A/B组分参数: 保存期限(A) = 6个月
2025-07-30 08:49:04,083 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 保留A/B组分参数: 保存期限(B) = 6个月
2025-07-30 08:49:04,083 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 硬度 -> 硬度 (置信度: 1.00)
2025-07-30 08:49:04,083 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 体积电阻率 -> 体积电阻率 (置信度: 1.00)
2025-07-30 08:49:04,083 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 绝缘强度 -> 绝缘强度 (置信度: 1.00)
2025-07-30 08:49:04,084 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 吸水率 -> 吸水率 (置信度: 1.00)
2025-07-30 08:49:04,084 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 拉伸强度 -> 拉伸强度 (置信度: 1.00)
2025-07-30 08:49:04,084 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 双85测试 -> 双85测试 (置信度: 1.00)
2025-07-30 08:49:04,084 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 未找到匹配的参数: 包装规格
2025-07-30 08:49:04,084 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 未找到匹配的参数: 规格1
2025-07-30 08:49:04,084 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 未找到匹配的参数: 规格2
2025-07-30 08:49:04,084 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 未找到匹配的参数: 规格3
2025-07-30 08:49:04,084 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 推断参数: 混合比例 重量比 100 -> 混合比例
2025-07-30 08:49:04,084 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 未找到匹配的参数: 注
2025-07-30 08:49:04,084 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 未找到匹配的参数: 上海富铭密封材料股份有限公司 编号
2025-07-30 08:49:04,084 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 未找到匹配的参数: 地址
2025-07-30 08:49:04,084 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 未找到匹配的参数: 电话
2025-07-30 08:49:04,084 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 未找到匹配的参数: 1. 预热
2025-07-30 08:49:04,084 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 未找到匹配的参数: 2. 混合
2025-07-30 08:49:04,084 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 未找到匹配的参数: 3. 脱泡
2025-07-30 08:49:04,084 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 未找到匹配的参数: 4. 浇注
2025-07-30 08:49:04,084 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 推断参数: 5. 固化 -> 固化时间
2025-07-30 08:49:04,084 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 未找到匹配的参数: 2. 搅拌
2025-07-30 08:49:04,084 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 未找到匹配的参数: 3. 装料
2025-07-30 08:49:04,084 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 未找到匹配的参数: 4. 程序设定
2025-07-30 08:49:04,084 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 未找到匹配的参数: 5. 空放
2025-07-30 08:49:04,084 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 未找到匹配的参数: 6. 浇筑
2025-07-30 08:49:04,085 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 推断参数: 7. 固化 -> 固化时间
2025-07-30 08:49:04,085 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 未找到匹配的参数: 储存运输 1. 储存条件
2025-07-30 08:49:04,085 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 未找到匹配的参数: 2. 储存期限
2025-07-30 08:49:04,085 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 未找到匹配的参数: 3. 使用环境
2025-07-30 08:49:04,085 - src.summary_table.parameter_mapper - INFO - logger.py:76 - 参数映射完成，映射了 16 个参数
2025-07-30 08:49:04,085 - src.validation.data_validator - INFO - logger.py:76 - 修复参数值: 外观(B) = 黄色透明/黑色液体 -> 黄色透明液体
2025-07-30 08:49:04,086 - src.validation.data_validator - WARNING - logger.py:80 - 参数格式异常: 体积电阻率 = Ω.cm，23℃
2025-07-30 08:49:04,086 - src.validation.data_validator - WARNING - logger.py:80 - 参数格式异常: 混合比例 = 100 企业标准
2025-07-30 08:49:04,087 - src.validation.data_validator - INFO - logger.py:76 - 数据验证和修复完成
2025-07-30 08:49:04,087 - src.summary_table.data_extractor - INFO - logger.py:76 - PDF提取完成: 16 个参数
2025-07-30 08:49:04,090 - src.summary_table.config_manager - INFO - logger.py:76 - 成功加载配置文件: config.json
2025-07-30 08:49:04,091 - src.extraction.base_extractor - INFO - logger.py:76 - PDF提取器初始化完成
2025-07-30 08:49:04,091 - src.extraction.base_extractor - INFO - logger.py:76 - Word提取器初始化完成
2025-07-30 08:49:04,092 - src.summary_table.parameter_mapper - INFO - logger.py:76 - 参数映射器初始化完成，支持 34 个参数
2025-07-30 08:49:04,092 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - A/B组分提取器初始化完成
2025-07-30 08:49:04,092 - src.summary_table.data_extractor - INFO - logger.py:76 - 技术数据提取器初始化完成
2025-07-30 08:49:04,092 - src.extraction.base_extractor - INFO - logger.py:76 - PDF提取器初始化完成
2025-07-30 08:49:04,092 - src.extraction.base_extractor - INFO - logger.py:76 - 开始提取PDF内容: FM-109-17技术参数表.pdf
2025-07-30 08:49:04,095 - src.extraction.base_extractor - DEBUG - logger.py:72 - 处理第 1 页
2025-07-30 08:49:04,243 - src.extraction.base_extractor - DEBUG - logger.py:72 - 第 1 页提取到表格 1: 4 行
2025-07-30 08:49:04,243 - src.extraction.base_extractor - DEBUG - logger.py:72 - 第 1 页提取到表格 2: 5 行
2025-07-30 08:49:04,243 - src.extraction.base_extractor - DEBUG - logger.py:72 - 第 1 页提取到表格 3: 8 行
2025-07-30 08:49:04,245 - src.extraction.base_extractor - DEBUG - logger.py:72 - 处理第 2 页
2025-07-30 08:49:04,336 - src.extraction.base_extractor - DEBUG - logger.py:72 - 第 2 页提取到表格 1: 4 行
2025-07-30 08:49:04,339 - src.extraction.base_extractor - DEBUG - logger.py:72 - 合并表格 1 和 2
2025-07-30 08:49:04,339 - src.extraction.base_extractor - INFO - logger.py:76 - 表格合并完成: 4 -> 3
2025-07-30 08:49:04,340 - src.extraction.base_extractor - DEBUG - logger.py:72 - 从页眉提取产品名称: FM-109-17
2025-07-30 08:49:04,340 - src.extraction.base_extractor - INFO - logger.py:76 - PDF内容提取完成: 3 个表格, 66 个段落
2025-07-30 08:49:04,340 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - A组分列匹配: '109-17A' -> 模式: ^.*A$
2025-07-30 08:49:04,340 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - B组分列匹配: '109-17B' -> 模式: ^.*B$
2025-07-30 08:49:04,340 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到有效A/B列: A列=2('109-17A'), B列=3('109-17B')
2025-07-30 08:49:04,340 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 从表头找到A/B列: (2, 3)
2025-07-30 08:49:04,340 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到核心参数: 外观 在 '外观'
2025-07-30 08:49:04,340 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到核心参数: 粘度 在 '粘度'
2025-07-30 08:49:04,340 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到核心参数: 密度 在 '密度'
2025-07-30 08:49:04,340 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - A/B组分表格验证通过，找到 3 个核心参数，1 个可选参数，外观参数: 是
2025-07-30 08:49:04,340 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到A/B组分表格，页面: 1
2025-07-30 08:49:04,340 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - A组分列匹配: '109-17A' -> 模式: ^.*A$
2025-07-30 08:49:04,340 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - B组分列匹配: '109-17B' -> 模式: ^.*B$
2025-07-30 08:49:04,340 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到有效A/B列: A列=2('109-17A'), B列=3('109-17B')
2025-07-30 08:49:04,341 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 从表头找到A/B列: (2, 3)
2025-07-30 08:49:04,341 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 检查A组分数据: '外观' -> 列2 = '黄色透明液体', 是否为单位: False
2025-07-30 08:49:04,341 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - ✓ 提取A组分: 外观(A) = 黄色透明液体
2025-07-30 08:49:04,341 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 检查B组分数据: '外观' -> 列3 = '黄色透明/黑色液体', 是否为单位: False
2025-07-30 08:49:04,341 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - ✓ 提取B组分: 外观(B) = 黄色透明/黑色液体
2025-07-30 08:49:04,341 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 检查A组分数据: '粘度' -> 列2 = '400±200', 是否为单位: False
2025-07-30 08:49:04,341 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - ✓ 提取A组分: 粘度(A) = 400±200
2025-07-30 08:49:04,342 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 检查B组分数据: '粘度' -> 列3 = '700±200', 是否为单位: False
2025-07-30 08:49:04,342 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - ✓ 提取B组分: 粘度(B) = 700±200
2025-07-30 08:49:04,342 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 检查A组分数据: '密度' -> 列2 = '1.10±0.05', 是否为单位: False
2025-07-30 08:49:04,342 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - ✓ 提取A组分: 密度(A) = 1.10±0.05
2025-07-30 08:49:04,342 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 检查B组分数据: '密度' -> 列3 = '0.96±0.05', 是否为单位: False
2025-07-30 08:49:04,342 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - ✓ 提取B组分: 密度(B) = 0.96±0.05
2025-07-30 08:49:04,343 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 检查A组分数据: '保存期限' -> 列2 = '6个月', 是否为单位: False
2025-07-30 08:49:04,343 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - ✓ 提取A组分: 保存期限(A) = 6个月
2025-07-30 08:49:04,343 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 检查B组分数据: '保存期限' -> 列3 = '6个月', 是否为单位: False
2025-07-30 08:49:04,343 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - ✓ 提取B组分: 保存期限(B) = 6个月
2025-07-30 08:49:04,344 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 未匹配参数: '混合比例' -> A: '100:100', B: '')
2025-07-30 08:49:04,344 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 跳过重复A组分: 粘度(A) (已存在: 400±200)
2025-07-30 08:49:04,344 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 跳过重复B组分: 粘度(B) (已存在: 700±200)
2025-07-30 08:49:04,344 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 跳过重复A组分: 密度(A) (已存在: 1.10±0.05)
2025-07-30 08:49:04,344 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 跳过重复B组分: 密度(B) (已存在: 0.96±0.05)
2025-07-30 08:49:04,344 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 未匹配参数: '凝胶时间' -> A: '90~150(100g)', B: '30~40(20g)')
2025-07-30 08:49:04,344 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 未匹配参数: '固化时间*' -> A: '60/3~6 | 23/48~72', B: '')
2025-07-30 08:49:04,344 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - A/B组分提取完成，提取到 8 个参数
2025-07-30 08:49:04,344 - src.summary_table.data_extractor - INFO - logger.py:76 - A/B组分提取器提取到 8 个参数
2025-07-30 08:49:04,344 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - A组分列匹配: '109-17A' -> 模式: ^.*A$
2025-07-30 08:49:04,344 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - B组分列匹配: '109-17B' -> 模式: ^.*B$
2025-07-30 08:49:04,344 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到有效A/B列: A列=2('109-17A'), B列=3('109-17B')
2025-07-30 08:49:04,344 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 从表头找到A/B列: (2, 3)
2025-07-30 08:49:04,344 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到核心参数: 外观 在 '外观'
2025-07-30 08:49:04,344 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到核心参数: 粘度 在 '粘度'
2025-07-30 08:49:04,344 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到核心参数: 密度 在 '密度'
2025-07-30 08:49:04,344 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - A/B组分表格验证通过，找到 3 个核心参数，1 个可选参数，外观参数: 是
2025-07-30 08:49:04,345 - src.summary_table.data_extractor - DEBUG - logger.py:72 - 从A/B组分表格提取非A/B参数: 0 个
2025-07-30 08:49:04,345 - src.summary_table.data_extractor - DEBUG - logger.py:72 - 从普通表格提取参数: 6 个
2025-07-30 08:49:04,345 - src.summary_table.data_extractor - DEBUG - logger.py:72 - 从普通表格提取参数: 4 个
2025-07-30 08:49:04,346 - src.summary_table.data_extractor - DEBUG - logger.py:72 - 总共提取到 37 个原始参数
2025-07-30 08:49:04,348 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - A/B组分提取器初始化完成
2025-07-30 08:49:04,349 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - A组分列匹配: '109-17A' -> 模式: ^.*A$
2025-07-30 08:49:04,349 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - B组分列匹配: '109-17B' -> 模式: ^.*B$
2025-07-30 08:49:04,349 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到有效A/B列: A列=2('109-17A'), B列=3('109-17B')
2025-07-30 08:49:04,349 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 从表头找到A/B列: (2, 3)
2025-07-30 08:49:04,349 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到核心参数: 外观 在 '外观'
2025-07-30 08:49:04,349 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到核心参数: 粘度 在 '粘度'
2025-07-30 08:49:04,349 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到核心参数: 密度 在 '密度'
2025-07-30 08:49:04,349 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - A/B组分表格验证通过，找到 3 个核心参数，1 个可选参数，外观参数: 是
2025-07-30 08:49:04,349 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到A/B组分表格，页面: 1
2025-07-30 08:49:04,349 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - A组分列匹配: '109-17A' -> 模式: ^.*A$
2025-07-30 08:49:04,349 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - B组分列匹配: '109-17B' -> 模式: ^.*B$
2025-07-30 08:49:04,349 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到有效A/B列: A列=2('109-17A'), B列=3('109-17B')
2025-07-30 08:49:04,349 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 从表头找到A/B列: (2, 3)
2025-07-30 08:49:04,349 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到核心参数: 外观 在 '外观'
2025-07-30 08:49:04,349 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到核心参数: 粘度 在 '粘度'
2025-07-30 08:49:04,349 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到核心参数: 密度 在 '密度'
2025-07-30 08:49:04,349 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - A/B组分表格验证通过，找到 3 个核心参数，1 个可选参数，外观参数: 是
2025-07-30 08:49:04,349 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到A/B组分表格，页面: 1
2025-07-30 08:49:04,349 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - A组分列匹配: '109-17A' -> 模式: ^.*A$
2025-07-30 08:49:04,349 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - B组分列匹配: '109-17B' -> 模式: ^.*B$
2025-07-30 08:49:04,349 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到有效A/B列: A列=2('109-17A'), B列=3('109-17B')
2025-07-30 08:49:04,349 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 从表头找到A/B列: (2, 3)
2025-07-30 08:49:04,349 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 检查A组分数据: '外观' -> 列2 = '黄色透明液体', 是否为单位: False
2025-07-30 08:49:04,349 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - ✓ 提取A组分: 外观(A) = 黄色透明液体
2025-07-30 08:49:04,349 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 检查B组分数据: '外观' -> 列3 = '黄色透明/黑色液体', 是否为单位: False
2025-07-30 08:49:04,349 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - ✓ 提取B组分: 外观(B) = 黄色透明/黑色液体
2025-07-30 08:49:04,350 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 检查A组分数据: '粘度' -> 列2 = '400±200', 是否为单位: False
2025-07-30 08:49:04,350 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - ✓ 提取A组分: 粘度(A) = 400±200
2025-07-30 08:49:04,350 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 检查B组分数据: '粘度' -> 列3 = '700±200', 是否为单位: False
2025-07-30 08:49:04,350 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - ✓ 提取B组分: 粘度(B) = 700±200
2025-07-30 08:49:04,350 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 检查A组分数据: '密度' -> 列2 = '1.10±0.05', 是否为单位: False
2025-07-30 08:49:04,350 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - ✓ 提取A组分: 密度(A) = 1.10±0.05
2025-07-30 08:49:04,350 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 检查B组分数据: '密度' -> 列3 = '0.96±0.05', 是否为单位: False
2025-07-30 08:49:04,350 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - ✓ 提取B组分: 密度(B) = 0.96±0.05
2025-07-30 08:49:04,350 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 检查A组分数据: '保存期限' -> 列2 = '6个月', 是否为单位: False
2025-07-30 08:49:04,350 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - ✓ 提取A组分: 保存期限(A) = 6个月
2025-07-30 08:49:04,350 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 检查B组分数据: '保存期限' -> 列3 = '6个月', 是否为单位: False
2025-07-30 08:49:04,350 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - ✓ 提取B组分: 保存期限(B) = 6个月
2025-07-30 08:49:04,350 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 未匹配参数: '混合比例' -> A: '100:100', B: '')
2025-07-30 08:49:04,350 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 跳过重复A组分: 粘度(A) (已存在: 400±200)
2025-07-30 08:49:04,350 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 跳过重复B组分: 粘度(B) (已存在: 700±200)
2025-07-30 08:49:04,350 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 跳过重复A组分: 密度(A) (已存在: 1.10±0.05)
2025-07-30 08:49:04,350 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 跳过重复B组分: 密度(B) (已存在: 0.96±0.05)
2025-07-30 08:49:04,350 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 未匹配参数: '凝胶时间' -> A: '90~150(100g)', B: '30~40(20g)')
2025-07-30 08:49:04,350 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 未匹配参数: '固化时间*' -> A: '60/3~6 | 23/48~72', B: '')
2025-07-30 08:49:04,350 - src.summary_table.ab_component_extractor - INFO - logger.py:76 - A/B组分提取完成，提取到 8 个参数
2025-07-30 08:49:04,351 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - A组分列匹配: '109-17A' -> 模式: ^.*A$
2025-07-30 08:49:04,351 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - B组分列匹配: '109-17B' -> 模式: ^.*B$
2025-07-30 08:49:04,351 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到有效A/B列: A列=2('109-17A'), B列=3('109-17B')
2025-07-30 08:49:04,351 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 从表头找到A/B列: (2, 3)
2025-07-30 08:49:04,351 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到核心参数: 外观 在 '外观'
2025-07-30 08:49:04,351 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到核心参数: 粘度 在 '粘度'
2025-07-30 08:49:04,351 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到核心参数: 密度 在 '密度'
2025-07-30 08:49:04,351 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - A/B组分表格验证通过，找到 3 个核心参数，1 个可选参数，外观参数: 是
2025-07-30 08:49:04,351 - src.summary_table.ab_component_extractor - DEBUG - logger.py:72 - 找到A/B组分表格，页面: 1
2025-07-30 08:49:04,352 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 保留A/B组分参数: 外观(A) = 黄色透明液体
2025-07-30 08:49:04,352 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 保留A/B组分参数: 外观(B) = 黄色透明/黑色液体
2025-07-30 08:49:04,352 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 保留A/B组分参数: 粘度(A) = 400±200
2025-07-30 08:49:04,352 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 保留A/B组分参数: 粘度(B) = 700±200
2025-07-30 08:49:04,352 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 保留A/B组分参数: 密度(A) = 1.10±0.05
2025-07-30 08:49:04,352 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 保留A/B组分参数: 密度(B) = 0.96±0.05
2025-07-30 08:49:04,352 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 保留A/B组分参数: 保存期限(A) = 6个月
2025-07-30 08:49:04,352 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 保留A/B组分参数: 保存期限(B) = 6个月
2025-07-30 08:49:04,352 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 硬度 -> 硬度 (置信度: 1.00)
2025-07-30 08:49:04,352 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 体积电阻率 -> 体积电阻率 (置信度: 1.00)
2025-07-30 08:49:04,352 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 绝缘强度 -> 绝缘强度 (置信度: 1.00)
2025-07-30 08:49:04,352 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 吸水率 -> 吸水率 (置信度: 1.00)
2025-07-30 08:49:04,352 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 拉伸强度 -> 拉伸强度 (置信度: 1.00)
2025-07-30 08:49:04,352 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 参数映射: 双85测试 -> 双85测试 (置信度: 1.00)
2025-07-30 08:49:04,352 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 未找到匹配的参数: 包装规格
2025-07-30 08:49:04,352 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 未找到匹配的参数: 规格1
2025-07-30 08:49:04,352 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 未找到匹配的参数: 规格2
2025-07-30 08:49:04,352 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 未找到匹配的参数: 规格3
2025-07-30 08:49:04,352 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 推断参数: 混合比例 重量比 100 -> 混合比例
2025-07-30 08:49:04,352 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 未找到匹配的参数: 注
2025-07-30 08:49:04,352 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 未找到匹配的参数: 上海富铭密封材料股份有限公司 编号
2025-07-30 08:49:04,352 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 未找到匹配的参数: 地址
2025-07-30 08:49:04,352 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 未找到匹配的参数: 电话
2025-07-30 08:49:04,352 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 未找到匹配的参数: 1. 预热
2025-07-30 08:49:04,352 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 未找到匹配的参数: 2. 混合
2025-07-30 08:49:04,353 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 未找到匹配的参数: 3. 脱泡
2025-07-30 08:49:04,353 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 未找到匹配的参数: 4. 浇注
2025-07-30 08:49:04,353 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 推断参数: 5. 固化 -> 固化时间
2025-07-30 08:49:04,353 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 未找到匹配的参数: 2. 搅拌
2025-07-30 08:49:04,353 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 未找到匹配的参数: 3. 装料
2025-07-30 08:49:04,353 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 未找到匹配的参数: 4. 程序设定
2025-07-30 08:49:04,353 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 未找到匹配的参数: 5. 空放
2025-07-30 08:49:04,353 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 未找到匹配的参数: 6. 浇筑
2025-07-30 08:49:04,353 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 推断参数: 7. 固化 -> 固化时间
2025-07-30 08:49:04,353 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 未找到匹配的参数: 储存运输 1. 储存条件
2025-07-30 08:49:04,353 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 未找到匹配的参数: 2. 储存期限
2025-07-30 08:49:04,353 - src.summary_table.parameter_mapper - DEBUG - logger.py:72 - 未找到匹配的参数: 3. 使用环境
2025-07-30 08:49:04,353 - src.summary_table.parameter_mapper - INFO - logger.py:76 - 参数映射完成，映射了 16 个参数
