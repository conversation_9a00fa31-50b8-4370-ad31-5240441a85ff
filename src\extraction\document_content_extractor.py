#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文档内容提取器
专门用于翻译功能的完整文档内容提取，保持格式和结构
"""

import os
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from docx import Document
from docx.shared import Inches
import pdfplumber
from .base_extractor import BaseExtractor
from ..utils import get_logger

logger = get_logger(__name__)


@dataclass
class DocumentElement:
    """文档元素"""
    element_type: str  # 'paragraph', 'table', 'image', 'header', 'footer'
    content: str
    formatting: Dict[str, Any] = None
    position: int = 0
    page: int = 1


@dataclass
class DocumentContent:
    """完整文档内容"""
    title: str = ""
    elements: List[DocumentElement] = None
    metadata: Dict[str, Any] = None
    total_pages: int = 1
    
    def __post_init__(self):
        if self.elements is None:
            self.elements = []
        if self.metadata is None:
            self.metadata = {}


class DocumentContentExtractor(BaseExtractor):
    """文档内容提取器（用于翻译）"""
    
    def __init__(self):
        super().__init__()
        self.logger.info("文档内容提取器初始化完成")
    
    def get_supported_extensions(self) -> List[str]:
        """获取支持的文件扩展名"""
        return ['.docx', '.doc', '.pdf']
    
    def extract_content(self, file_path: str) -> Optional[DocumentContent]:
        """
        提取完整文档内容
        
        Args:
            file_path: 文件路径
            
        Returns:
            DocumentContent: 完整文档内容
        """
        try:
            if not os.path.exists(file_path):
                self.logger.error(f"文件不存在: {file_path}")
                return None
            
            file_ext = os.path.splitext(file_path.lower())[1]
            
            if file_ext in ['.docx', '.doc']:
                return self._extract_word_content(file_path)
            elif file_ext == '.pdf':
                return self._extract_pdf_content(file_path)
            else:
                self.logger.warning(f"不支持的文件格式: {file_ext}")
                return None
                
        except Exception as e:
            self.logger.error(f"文档内容提取失败 {file_path}: {e}")
            return None
    
    def _extract_word_content(self, file_path: str) -> Optional[DocumentContent]:
        """提取Word文档完整内容"""
        try:
            doc = Document(file_path)
            content = DocumentContent()
            
            # 提取元数据
            if doc.core_properties:
                content.metadata = {
                    'title': doc.core_properties.title or "",
                    'author': doc.core_properties.author or "",
                    'created': str(doc.core_properties.created) if doc.core_properties.created else "",
                    'modified': str(doc.core_properties.modified) if doc.core_properties.modified else ""
                }
                content.title = content.metadata['title']
            
            position = 0
            
            # 提取页眉
            for section in doc.sections:
                if section.header:
                    header_text = self._extract_header_footer_text(section.header)
                    if header_text.strip():
                        content.elements.append(DocumentElement(
                            element_type='header',
                            content=header_text,
                            position=position
                        ))
                        position += 1
            
            # 提取文档主体内容
            for element in doc.element.body:
                if element.tag.endswith('p'):
                    # 段落
                    paragraph = self._find_paragraph_by_element(doc, element)
                    if paragraph and paragraph.text.strip():
                        formatting = self._extract_paragraph_formatting(paragraph)
                        content.elements.append(DocumentElement(
                            element_type='paragraph',
                            content=paragraph.text,
                            formatting=formatting,
                            position=position
                        ))
                        position += 1
                
                elif element.tag.endswith('tbl'):
                    # 表格
                    table = self._find_table_by_element(doc, element)
                    if table:
                        table_content = self._extract_table_content_for_translation(table)
                        content.elements.append(DocumentElement(
                            element_type='table',
                            content=table_content,
                            position=position
                        ))
                        position += 1
            
            # 提取页脚
            for section in doc.sections:
                if section.footer:
                    footer_text = self._extract_header_footer_text(section.footer)
                    if footer_text.strip():
                        content.elements.append(DocumentElement(
                            element_type='footer',
                            content=footer_text,
                            position=position
                        ))
                        position += 1
            
            self.logger.info(f"Word文档内容提取完成: {len(content.elements)} 个元素")
            return content
            
        except Exception as e:
            self.logger.error(f"Word文档内容提取失败: {e}")
            return None
    
    def _extract_pdf_content(self, file_path: str) -> Optional[DocumentContent]:
        """提取PDF文档完整内容"""
        try:
            content = DocumentContent()
            position = 0
            
            with pdfplumber.open(file_path) as pdf:
                content.total_pages = len(pdf.pages)
                
                # 提取元数据
                if pdf.metadata:
                    content.metadata = dict(pdf.metadata)
                    content.title = content.metadata.get('Title', '')
                
                for page_num, page in enumerate(pdf.pages, 1):
                    # 提取页面文本
                    page_text = page.extract_text()
                    if page_text:
                        # 按段落分割
                        paragraphs = page_text.split('\n\n')
                        for para in paragraphs:
                            if para.strip():
                                content.elements.append(DocumentElement(
                                    element_type='paragraph',
                                    content=para.strip(),
                                    position=position,
                                    page=page_num
                                ))
                                position += 1
                    
                    # 提取表格
                    tables = page.extract_tables()
                    for table in tables:
                        if table:
                            table_content = self._format_table_for_translation(table)
                            content.elements.append(DocumentElement(
                                element_type='table',
                                content=table_content,
                                position=position,
                                page=page_num
                            ))
                            position += 1
            
            self.logger.info(f"PDF文档内容提取完成: {len(content.elements)} 个元素, {content.total_pages} 页")
            return content
            
        except Exception as e:
            self.logger.error(f"PDF文档内容提取失败: {e}")
            return None
    
    def _extract_paragraph_formatting(self, paragraph) -> Dict[str, Any]:
        """提取段落格式信息"""
        formatting = {}
        
        try:
            # 段落级别格式
            if paragraph.style:
                formatting['style'] = paragraph.style.name
            
            # 对齐方式
            if paragraph.alignment:
                formatting['alignment'] = str(paragraph.alignment)
            
            # 字体格式（取第一个run的格式作为代表）
            if paragraph.runs:
                first_run = paragraph.runs[0]
                if first_run.font:
                    formatting['font'] = {
                        'name': first_run.font.name,
                        'size': first_run.font.size.pt if first_run.font.size else None,
                        'bold': first_run.font.bold,
                        'italic': first_run.font.italic,
                        'underline': first_run.font.underline
                    }
        
        except Exception as e:
            self.logger.debug(f"格式提取失败: {e}")
        
        return formatting
    
    def _extract_table_content_for_translation(self, table) -> str:
        """提取表格内容用于翻译（保持结构）"""
        try:
            table_lines = []
            
            for row in table.rows:
                row_cells = []
                for cell in row.cells:
                    cell_text = cell.text.strip()
                    row_cells.append(cell_text)
                
                # 使用制表符分隔单元格
                table_lines.append('\t'.join(row_cells))
            
            return '\n'.join(table_lines)
            
        except Exception as e:
            self.logger.debug(f"表格内容提取失败: {e}")
            return ""
    
    def _format_table_for_translation(self, table_data: List[List[str]]) -> str:
        """格式化表格数据用于翻译"""
        try:
            table_lines = []
            
            for row in table_data:
                if row:
                    # 清理单元格内容
                    cleaned_row = [str(cell).strip() if cell else "" for cell in row]
                    table_lines.append('\t'.join(cleaned_row))
            
            return '\n'.join(table_lines)
            
        except Exception as e:
            self.logger.debug(f"表格格式化失败: {e}")
            return ""
    
    def _extract_header_footer_text(self, header_footer) -> str:
        """提取页眉页脚文本"""
        try:
            text_parts = []
            for paragraph in header_footer.paragraphs:
                if paragraph.text.strip():
                    text_parts.append(paragraph.text.strip())
            return '\n'.join(text_parts)
        except Exception:
            return ""
    
    def _find_paragraph_by_element(self, doc, element):
        """根据元素查找对应的段落对象"""
        try:
            for paragraph in doc.paragraphs:
                if paragraph._element == element:
                    return paragraph
        except Exception:
            pass
        return None
    
    def _find_table_by_element(self, doc, element):
        """根据元素查找对应的表格对象"""
        try:
            for table in doc.tables:
                if table._element == element:
                    return table
        except Exception:
            pass
        return None
